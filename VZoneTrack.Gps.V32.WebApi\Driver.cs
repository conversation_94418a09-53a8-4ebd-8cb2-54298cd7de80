//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace VZoneTrack.Gps.V32.WebApi
{
    using System;
    using System.Collections.Generic;
    
    public partial class Driver
    {
        public long ID { get; set; }
        public string Code { get; set; }
        public long CompanyID { get; set; }
        public string DriverName { get; set; }
        public string DellasKey { get; set; }
        public string LicenseNo { get; set; }
        public Nullable<System.DateTime> LicenseIssueDate { get; set; }
        public Nullable<System.DateTime> LicenseExpiryDate { get; set; }
        public string MobileNo { get; set; }
        public string Email { get; set; }
        public string Designation { get; set; }
        public string Picture { get; set; }
        public bool IsActive { get; set; }
        public bool IsDelete { get; set; }
        public Nullable<long> CreatedBy { get; set; }
        public System.DateTime CreatedDate { get; set; }
        public Nullable<long> ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public bool IsDriver { get; set; }
        public Nullable<short> TempCardType { get; set; }
        public Nullable<int> DriverCardID { get; set; }
        public string IDNumber { get; set; }
        public string Nationality { get; set; }
        public Nullable<System.DateTime> DOB { get; set; }
        public Nullable<System.DateTime> ExpiryDate { get; set; }
        public Nullable<System.DateTime> IssueDate { get; set; }
        public bool IsSystemDefined { get; set; }
        public string Remarks { get; set; }
        public bool IsCase_Authenticated { get; set; }
        public Nullable<System.DateTime> IsCase_Authenticated_At { get; set; }
        public Nullable<long> IsCase_Authenticated_RefId { get; set; }
        public bool IsCase_Tokenized { get; set; }
        public Nullable<int> IsCase_VehiclePersonnelAllocationId { get; set; }
    }
}
