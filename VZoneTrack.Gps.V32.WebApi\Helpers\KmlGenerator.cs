﻿using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Web;
using System.Xml.Linq;
using VZoneTrack.Gps.V32.WebApi.Models;

namespace VZoneTrack.Gps.V32.WebApi.Helpers
{
    public static class KmlGenerator
    {
        private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// Creates a KML file from route points with customizable metadata and style.
        /// </summary>
        /// <param name="routePoints">List of route points with latitude and longitude.</param>
        /// <param name="name">Route name (e.g., "Route for A-44558").</param>
        /// <param name="description">Route description (optional).</param>
        /// <param name="lineColor">Line color in AABBCCDD format (default: red, ff0000ff).</param>
        /// <param name="lineWidth">Line width in pixels (default: 4).</param>
        /// <param name="outputPath">Path to save KML file. If null, returns KML as string.</param>
        /// <returns>Path to KML file or KML content as string.</returns>
        public static string CreateKmlFile(IEnumerable<(double Latitude, double Longitude)> routePoints, string name, string description = null, string lineColor = "ff0000ff", double lineWidth = 4)
        {
            StringBuilder kmlText = new StringBuilder();

            if (routePoints == null || !routePoints.Any())
            {
                throw new ArgumentException("Route points cannot be null or empty.");
            }

            try
            {
                XNamespace ns = "http://www.opengis.net/kml/2.2";

                var kml = new XDocument(
                    new XElement(ns + "kml",
                        new XElement(ns + "Document",
                            new XElement(ns + "name", name ?? "Route"),
                            description != null ? new XElement(ns + "description", description) : null,
                            new XElement(ns + "Style", new XAttribute("id", "routeStyle"),
                                new XElement(ns + "LineStyle",
                                    new XElement(ns + "color", lineColor),
                                    new XElement(ns + "width", lineWidth.ToString())
                                )
                            ),
                            new XElement(ns + "Placemark",
                                new XElement(ns + "name", "Route"),
                                new XElement(ns + "styleUrl", "#routeStyle"),
                                new XElement(ns + "LineString",
                                    new XElement(ns + "tessellate", "1"),
                                    new XElement(ns + "coordinates",
                                        string.Join(" ", routePoints.Select(p =>
                                            $"{p.Longitude},{p.Latitude},0"))
                                    )
                                )
                            )
                        )
                    )
                );

                kmlText.Append(kml.ToString());

            }
            catch (Exception ex)
            {
                log.Error("KmlGenerator.CreateKmlFile: " + ex);
                throw;
            }

            return kmlText.ToString();
        }

        public static string CreateKmlFile(IEnumerable<RouteInfo> routePoints, string name, string description = null, string lineColor = "ff0000ff", double lineWidth = 4)
        {
            if (routePoints == null || !routePoints.Any())
                throw new ArgumentException("Route points cannot be null or empty.");

            StringBuilder kmlText = new StringBuilder();

            try
            {
                XNamespace ns = "http://www.opengis.net/kml/2.2";

                var document = new XElement(ns + "Document",
                    new XElement(ns + "name", name ?? "Route"),
                    description != null ? new XElement(ns + "description", description) : null,
                                        
                    new XElement(ns + "Style", new XAttribute("id", "routeStyle"),
                        new XElement(ns + "LineStyle",
                            new XElement(ns + "color", lineColor),
                            new XElement(ns + "width", lineWidth.ToString())
                        )
                    ),
                                        
                    new XElement(ns + "Style", new XAttribute("id", "idleIcon"),
                        new XElement(ns + "IconStyle",
                            new XElement(ns + "scale", "2.0"),
                            new XElement(ns + "Icon",
                                new XElement(ns + "href", "https://www.vzoneinternational.ae/VZoneTrack.Gps.V32/img/alert_excessidle.png")
                            )
                        )
                    ),
                                        
                    new XElement(ns + "Style", new XAttribute("id", "stopIcon"),
                        new XElement(ns + "IconStyle",
                            new XElement(ns + "scale", "2.0"),
                            new XElement(ns + "Icon",
                                new XElement(ns + "href", "https://www.vzoneinternational.ae/VZoneTrack.Gps.V32/img/alert_excessstop.png")
                            )
                        )
                    )
                );

                for (int d = 0; d < 360; d++)
                {
                    document.Add(
                        new XElement(ns + "Style", new XAttribute("id", $"dirIcon{d}"),
                            new XElement(ns + "IconStyle",
                                new XElement(ns + "scale", "1.2"),
                                new XElement(ns + "Icon",
                                    new XElement(ns + "href", $"https://www.vzoneinternational.ae/VZoneTrack.Archives/images/dir_icons/{d}.png?v={DateTime.UtcNow.Ticks}")
                                ),
                                new XElement(ns + "hotSpot",
                                    new XAttribute("x", "0.5"),
                                    new XAttribute("y", "0.5"),
                                    new XAttribute("xunits", "fraction"),
                                    new XAttribute("yunits", "fraction")
                                )
                            )
                        )
                    );
                }

                document.Add(
                    new XElement(ns + "Placemark",
                        new XElement(ns + "name", "Route Path"),
                        new XElement(ns + "styleUrl", "#routeStyle"),
                        new XElement(ns + "LineString",
                            new XElement(ns + "tessellate", "1"),
                            new XElement(ns + "coordinates",
                                string.Join(" ", routePoints.Select(p =>
                                    $"{p.Longitude},{p.Latitude},0"))
                            )
                        )
                    )
                );
                                
                foreach (var p in routePoints)
                {
                    string styleId;
                    switch (p.Ignition.ToUpper())
                    {
                        case "IDLE":
                            styleId = "#idleIcon";
                            break;
                        case "STOP":
                            styleId = "#stopIcon";
                            break;
                        default:
                            int roundedDirection = (int)p.Direction;
                            styleId = $"#dirIcon{roundedDirection}";
                            break;
                    }

                    document.Add(
                        new XElement(ns + "Placemark",
                            new XElement(ns + "name", $"{p.TrackTime:HH:mm:ss}"),
                            new XElement(ns + "description", $"Ignition: {p.Ignition}, Heading: {p.Direction}°"),
                            new XElement(ns + "styleUrl", styleId),
                            new XElement(ns + "Point",
                                new XElement(ns + "coordinates", $"{p.Longitude},{p.Latitude},0")
                            )
                        )
                    );
                }

                var kml = new XDocument(
                    new XElement(ns + "kml",
                        document
                    )
                );

                kmlText.Append(kml.ToString());
            }
            catch (Exception ex)
            {
                log.Error("KmlGenerator.CreateKmlFile: " + ex);
                throw;
            }

            return kmlText.ToString();
        }


    }
}
