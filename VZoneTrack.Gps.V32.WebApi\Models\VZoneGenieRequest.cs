﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace VZoneTrack.Gps.V32.WebApi.Models
{
    public class VZoneGenieRequest
    {
        public RequestType RequestType { get; set; }
        public DateTime? From { get; set; }
        public DateTime? To { get; set; }
        public List<int> VehicleIds { get; set; } = new List<int>();
        public List<int> DriverIds { get; set; } = new List<int>();
        public List<OperationRequest> Operations { get; set; } = new List<OperationRequest>();
        public int? TopN { get; set; } // For "top 5", "top 10", etc. requests
    }

    public class OperationRequest
    {
        public OperationType OperationType { get; set; }

        public List<GroupBySpec> GroupByColumns { get; set; } = new List<GroupBySpec>();

        public List<ColumnOperation> ColumnOperations { get; set; } = new List<ColumnOperation>();

        public List<LookupOperation> LookupOperations { get; set; } = new List<LookupOperation>();

        public CalculationDetails Calculation { get; set; }

        public int? TopN { get; set; } // For operation-level top N requests
    }

    public class ColumnSpec
    {
        public string Name { get; set; } // e.g. TripDate, VehicleId
        public string DataType { get; set; } // e.g. DateTime, String, Decimal
    }

    public class GroupBySpec
    {
        public string Name { get; set; }
        public string DataType { get; set; }
    }

    public class ColumnOperation
    {         
        public string SourceColumn { get; set; }
        public string SourceDataType { get; set; }
        public string AggregateFunction { get; set; } // e.g. SUM, AVG, MAX
        public string ResponseText { get; set; }        
        public CalculationDetails Calculation { get; set; } // For compute operations
    }

    public class LookupOperation
    {
        public string SourceColumn { get; set; }
        public string SourceDataType { get; set; }
        public LookupOperationType OperationType { get; set; }
        public string TargetColumn { get; set; }
        public string TargetDataType { get; set; }
        public string KeyColumn { get; set; } // e.g., AnalyticsTripSummary.VehicleId
        public string KeyDataType { get; set; } // e.g., Int
        public bool ReturnKeyColumn { get; set; }
        public string CalculationMethod { get; set; } // optional: if calculation applied within lookup
        public List<FilterCondition> Filters { get; set; } = new List<FilterCondition>();
        public List<JoinCondition> Joins { get; set; } = new List<JoinCondition>();
        public List<GroupBySpec> GroupByColumns { get; set; } = new List<GroupBySpec>();
        public int? TopN { get; set; } // For lookup-level top N requests
        public string OrderByColumn { get; set; } // Column to order by for TopN
        public string OrderDirection { get; set; } = "DESC"; // ASC or DESC
    }

    public class FilterCondition
    {
        public string Column { get; set; }
        public string Operator { get; set; }
        public string Value { get; set; }
    }

    public class CalculationDetails
    {
        public string Formula { get; set; }
        public List<CalculationOperand> Operands { get; set; } = new List<CalculationOperand>();
    }

    public class CalculationOperand
    {
        public string Name { get; set; }
        public string DataType { get; set; }
        public OperationType Operation { get; set; }
        public string AggregateFunction { get; set; }
    }

    public enum OperationType
    {
        Raw,
        Aggregate,
        Compute,
        Lookup
    }

    public enum LookupOperationType
    {
        Raw,
        Max,
        Min,
        First,
        Last
    }

    public enum RequestType
    {
        Live,
        Route,
        Summary,
        Detail
    }

    public class JoinCondition
    {
        public string Table { get; set; }
        public string JoinType { get; set; } // e.g., INNER, LEFT
        public string Condition { get; set; } // e.g., AnalyticsTripSummary.VehicleId = Vehicle.ID
    }
}