<?xml version="1.0"?>
<DocComments>
  <ExpressionImports>
    <Class>
      <summary>
        Holds all the types whose static members can be used in an expression.
      </summary>
      <remarks>
        Use this class to allow the static functions, methods, and properties of a type
        to be used in an expression.  By default, no types are imported.
      </remarks>
      <example>
        This example shows how to use this class to let an expression use all the static members of the Math class:
        <code lang="C#">
          // Define the context of our expression
          ExpressionContext context = new ExpressionContext();
          // Import all members of the Math type into the default namespace
          context.Imports.AddType(typeof(Math));
        </code>
      </example>
    </Class>

    <AddType1>
      <overloads>Makes the static and public members of a type available for use in an expression</overloads>
      <summary>Imports all public and static members of a type into a specific namespace and makes them available to an expression.</summary>
      <param name="t">The type to import</param>
      <param name="ns">The namespace to import the type into -or- the empty string to import into the default namespace</param>
      <remarks>
        Use this method to import a type into an expression.  All static and public methods, fields, and properties of the type will be
        directly accessible in the expression.  If the namespace parameter is the empty string, the type's members will be able to be referenced
        without any qualification.  Otherwise, they will be imported into the specified namespace and will need to be qualified with it before being accessed.  The
        imported type is deemed accessible if it is either public or in the same module as the expression owner.
      </remarks>
      <example>
        If you import the Math type into the default namespace, you can reference its members in the expression: "cos(1.0)".
        If you import same type into the "Math" namespace, you will need to qualify its members like so: "math.cos(1.0)".
      </example>
      <exception cref="T:System.ArgumentException">The imported type is not accessible</exception>
    </AddType1>

    <AddType2>
      <summary>Imports all public and static members of a type into the default namespace and makes them available to an expression.</summary>
      <param name="t">The type to import</param>
      <remarks>
        Use this method to import a type into an expression.  All static and public methods, fields, and properties of the type will be
        directly accessible in the expression.

        See <see cref="M:Ciloci.Flee.ExpressionImports.AddType(System.Type,System.String)"></see> for more details.
      </remarks>
      <exception cref="T:System.ArgumentException">The imported type is not accessible</exception>
    </AddType2>

    <AddMethod1>
      <overloads>Imports a single public and static method into an expression.</overloads>
      <summary>Imports a public and static method of a type into a given namespace and makes it available to an expression.</summary>
      <param name="methodName">The name of the method to import</param>
      <param name="t">The type on which to lookup the method</param>
      <param name="ns">The namespace to import the method into -or- the empty string to import into the default namespace</param>
      <remarks>
        Use this method to import a single static, public method into an expression.  The method with the given name will be looked
        up on the type argument and added into the specified namespace.
      </remarks>
      <exception cref="T:System.ArgumentException">The type the method belongs to is not accessible</exception>
    </AddMethod1>

    <AddMethod2>
      <summary>Imports a public and static method of a type into a given namespace and makes it available to an expression.</summary>
      <param name="mi">The method to import</param>
      <param name="ns">The namespace to import the method into -or- the empty string to import into the default namespace</param>
      <remarks>
        Use this method to import a single static, public method into an expression.  This overload is used when you already have a specific
        MethodInfo available.
      </remarks>
      <exception cref="T:System.ArgumentException">The type the method belongs to is not accessible</exception>
    </AddMethod2>

    <ImportBuiltinTypes>
      <summary>
        Imports the builtin types into an expression
      </summary>
      <remarks>
        Call this method to import the builtin types (int, string, double) into an expression.  After this method is called, you can use members of the
        builtin types in an expression ie: <pre>int.maxvalue * 2</pre>
      </remarks>
    </ImportBuiltinTypes>

    <RootImport>
      <summary>
        Gets the root import of the context.
      </summary>
      <value>The root import.</value>
      <remarks>
        Use this property to access the root import of an expression.
      </remarks>
    </RootImport>    
  </ExpressionImports>  
  <ImportBase>
    <Class>
      <summary>Base class for all expression imports</summary>      
    </Class>

    <Name>
      <summary>Gets the name of the import</summary>
      <value>The name of the current import instance</value>
      <remarks>Use this property to get the name of the import</remarks>
    </Name>

    <IsContainer>
      <summary>Determines if this import can contain other imports</summary>
      <value>True if this import can contain other imports; False otherwise</value>
      <remarks>Use this property to determine if this import contains other imports</remarks>
    </IsContainer>
  </ImportBase>
  <TypeImport>
    <Class>
      <summary>Represents an imported type</summary>
      <remarks>Use this class when you want to make the members of a type available to an expression</remarks>
    </Class>
    <New1>
      <summary>Creates a new import with a given type</summary>
      <param name="importType">The type to import</param>      
    </New1>
    <New2>
      <summary>Creates a new import with a given type</summary>
      <param name="importType">The type to import</param>
      <param name="useTypeNameAsNamespace">True to use the type's name as a namespace; False otherwise</param>
      <remarks>When useTypeNameAsNamespace is set to True, the type will act as a namespace in an expression.  For example: If
      you import the DayOfWeek enum and set the flag to true, you can reference it as DayOfWeek.Sunday in an expression.  When the flag is false,
      you would reference it as simply Sunday.</remarks>
    </New2>    
    <Target>
      <summary>Gets the type that this import represents</summary>
      <value>The type that this import represents</value>
      <remarks>Use this property to retrieve the imported type</remarks>
    </Target>
  </TypeImport>
  <MethodImport>
    <Class>
      <summary>Represents an imported method</summary>
      <remarks>Use this class when you want to make a single method available to an expression</remarks>
    </Class>
    <New>
      <summary>Creates a new method import with a given method</summary>
      <param name="importMethod">The method to import</param>
    </New>
    <Target>
      <summary>Gets the method that this import represents</summary>
      <value>The method that this import represents</value>
      <remarks>Use this property to retrieve the imported method</remarks>
    </Target>
  </MethodImport>
  <NamespaceImport>
    <Class>
      <summary>Represents an imported namespace</summary>
      <remarks>This class acts as a container for other imports.  Use it when you want to logically group expression imports.</remarks>
    </Class>
    <New>
      <summary>Creates a new namespace import with a given namespace name</summary>
      <param name="importNamespace">The name of the namespace to import</param>
    </New>
  </NamespaceImport>  
  <ExpressionOptions>
    <Class>
      <summary>
        Allows customization of expression compilation.
      </summary>
      <remarks>
        Use this class when you need to customize how an expression is compiled.  For example: by setting
        the <see cref="P:Ciloci.Flee.ExpressionOptions.Checked">Checked</see> property to true,
        you can cause all arithmetic and conversion operations to check for overflow.
      </remarks>
    </Class>

    <StringComparison>
      <summary>
        Gets or sets a value that determines how strings will be compared.
      </summary>
      <value>The type of string comparison to use.</value>
      <remarks>
        Use this property to control the type of string comparison used in an expression that compares two strings.  For example: the result of
        the expression <span style="font-family: monospace;">"string" = "STRING"</span> will be <b>true</b> if the string comparison is set to ignore case
        and <b>false</b> otherwise.  The default is Ordinal.
      </remarks>
    </StringComparison>

    <CaseSensitive>
      <summary>
        Determines how an expression matches member and variable names
      </summary>
      <value>True to respect case when matching; False to ignore case</value>
      <remarks>
        Use this property to control how an expression resolves member and variable names.  If set to true, variable and member
        names will be matched in a case-sensitive manner.  When false, case will be ignored.
      </remarks>
    </CaseSensitive>

    <ResultType>
      <summary>Gets or sets the type of the expression's result.</summary>
      <value>
        A <see cref="T:System.Type"/> indicating the desired result type.
      </value>
      <remarks>
        Use this property to convert the result of an expression to a particular type.  Essentially, it acts as an implicit conversion from the final
        result of the expression to the given type.  When this property is set, the expression will attempt to convert its result to the set value.
        If the conversion is invalid, an <see cref="T:Ciloci.Flee.ExpressionCompileException"/> will be thrown.
      </remarks>
    </ResultType>

    <Checked>
      <summary>
        Gets or sets whether arithmetic and conversion operations check for overflow.
      </summary>
      <value>True to emit overflow checks.  False to emit no overflow checks.</value>
      <remarks>
        Setting this property to true will cause all arithmetic and conversion operations to emit overflow checks.  When
        one of those operations is executed and the resultant value cannot fit into the result type, an <see cref="T:System.OverflowException">OverflowException</see>
        will be thrown.
      </remarks>    
    </Checked>

    <EmitToAssembly>
      <summary>
        Determines if the expression's IL will be saved to an assembly on disk.
      </summary>
      <value>True to save the expression's IL to an assembly; false otherwise.</value>
      <remarks>
        Use this method when you want to inspect the IL that an expression emits.  When set to true, the expression will save its IL to the assembly "Expression.dll"
        on disk.  You can then view the IL with a disassembler.
      </remarks>
    </EmitToAssembly>

    <OwnerMemberAccess>
      <summary>
        Determines which members on the expression owner are accessible.
      </summary>
      <value>A combination of BindingFlags that determine which members are accessible.</value>
      <remarks>
        Using this property, you can control which members on the expression owner are accessible from an expression.  For example: if users
        will be inputing expressions, you can prevent private members on the expression owner from being used.  You can use the
        <see cref="T:Ciloci.Flee.ExpressionOwnerMemberAccessAttribute"/> attribute on individual members to override the access set using this property.
        <note>
          The default is to only allow access to public members on the expression owner.  Currently, only the Public and NonPublic values of the BindingFlags enumeration
          are used.
        </note>
      </remarks>
    </OwnerMemberAccess>

    <ParseCulture>
      <summary>Gets or sets the culture to use when parsing expressions</summary>
      <value>The culture to use</value>
      <remarks>
        Use this property to allow for parsing of expressions using culture-specific tokens.  This is useful, for example, when you
        wish to parse numbers using a culture-specific decimal separator.
      </remarks>
    </ParseCulture>

    <IntegersAsDoubles>
      <summary>Gets or sets whether all integer literals are treated as doubles</summary>
      <value>True to treat all integer literals as doubles; False to use integers for integral numbers and floating point for real numbers (ie: any number with a decimal point)</value>
      <remarks>
        Use this property to force all integer literals to doubles.  When set to true, an expression like "1/2" will return 0.5 since both integer
        literals are treated as doubles.  When false, the same expression will return 0 since an integer division will be performed.
      </remarks>
    </IntegersAsDoubles>

    <RealLiteralDataType>
      <summary>Gets or sets the data type used for real literals when no explicit type is specified.</summary>
      <value>A value specifying the type to use</value>
      <remarks>
        Use this property to set the data type that will be used to represent real literals.  For example: if set to Decimal, all real literals (ie: 100.45) will be parsed into 
        a System.Decimal value.
      </remarks>
    </RealLiteralDataType>

  </ExpressionOptions>

  <ExpressionParserOptions>
    <Class>
      <summary>
        Holds settings which enable customization of the expression parser.
      </summary>
    </Class>
    <RecreateParser>
      <summary>
        Updates the expression parser using the settings provided in this class.
      </summary>
    </RecreateParser>
    <DateTimeFormat>
      <summary>Gets or sets the format to use for parsing DateTime literals</summary>
      <value>The format to use</value>
      <remarks>
        Use this property to set the format that will be used to parse DateTime literals.  Expressions which have DateTime literals that are not parseable using this format will fail to compile.
      </remarks>
      <example>When set to "dd-MM-yyyy", an expression such as "#04-07-2008#" would be parsed to the corresponding DateTime value.</example>
    </DateTimeFormat>
    <RequireDigitsBeforeDecimalPoint>
      <summary>Gets or sets a value that determines if literals for real numbers must have digits before the decimal point.</summary>
      <value>True to require digits (ie: 0.56); False otherwise (ie: .56)</value>
    </RequireDigitsBeforeDecimalPoint>
    <DecimalSeparator>
      <summary>Gets or sets the character to use as the decimal separator for real number literals.</summary>
    </DecimalSeparator>
    <FunctionArgumentSeparator>
      <summary>Gets or sets the character to use to separate function arguments.</summary>
    </FunctionArgumentSeparator>
  </ExpressionParserOptions>
  
  <ExpressionCompileException>
    <Class>
      <summary>
        The exception thrown when an expression cannot be compiled.
      </summary>
      <remarks>
        This exception is thrown whenever an expression cannot be compiled.
        The <see cref="P:Ciloci.Flee.ExpressionCompileException.Reason">Reason</see> property
        will contain a value indicating the specific cause of the exception.
      </remarks>
    </Class>

    <Reason>
      <summary>
        Gets the reason why compilation failed.
      </summary>
      <value>A value indicating the cause of the exception</value>
      <remarks>Use this property to determine the reason why compilation failed.</remarks>
    </Reason>
  </ExpressionCompileException>
  <CompileExceptionReason>
    <Class>
      <summary>
        Defines values to indicate why compilation of an expression failed.
      </summary>
      <remarks>
        When compilation of an expression fails, an <see cref="T:Ciloci.Flee.ExpressionCompileException"/> will be thrown.
        The <see cref="P:Ciloci.Flee.ExpressionCompileException.Reason">Reason</see> property
        on the exception will contain a value from this enumeration.  You can use that value to determine how to handle the exception.
        For example: if the Reason is a SyntaxError, you can display an error message tailored to syntax errors.
      </remarks>
    </Class>

    <SyntaxError>
      <summary>The expression text is not parsable because it does not meet the syntax rules of the expression grammar.</summary>
    </SyntaxError>

    <ConstantOverflow>
      <summary>A constant expression cannot be represented in its type.</summary>
    </ConstantOverflow>

    <TypeMismatch>
      <summary>The operation is invalid for the given type.</summary>
    </TypeMismatch>

    <UndefinedName>
      <summary>The expression references a name that cannot be resolved.</summary>
    </UndefinedName>

    <FunctionHasNoReturnValue>
      <summary>The expression calls a function that does not return a value.</summary>
    </FunctionHasNoReturnValue>

    <InvalidExplicitCast>
      <summary>The requested explicit cast is not valid for the given types.</summary>
    </InvalidExplicitCast>

    <AmbiguousMatch>
      <summary>More than one member matches the required criteria.</summary>
    </AmbiguousMatch>

    <AccessDenied>
      <summary>Access to the specified member is not allowed.</summary>
    </AccessDenied>

    <InvalidFormat>
      <summary>The given value is not in the required format.</summary>
    </InvalidFormat>
  </CompileExceptionReason>
  <RealLiteralDataType>
    <Class>
      <summary>
        Defines values to indicate the data type to use for storing real literals.
      </summary>      
    </Class>

    <Single>
      <summary>Specifies that real literals will be stored using the <see cref="T:System.Single"/> data type.</summary>
    </Single>
    <Double>
      <summary>
        Specifies that real literals will be stored using the <see cref="T:System.Double"/> data type.
      </summary>
    </Double>
    <Decimal>
      <summary>
        Specifies that real literals will be stored using the <see cref="T:System.Decimal"/> data type.
      </summary>
    </Decimal>
  </RealLiteralDataType>
  <VariableCollection>
    <Class>
      <summary>
        Manages the variables available to an expression
      </summary>
      <remarks>
        Use this class to manage the variables that an expression can use
      </remarks>
    </Class>

    <Clear>
      <summary>Removes all variables from the collection.</summary>
      <remarks>Use this method to remove all variables from the collection</remarks>
    </Clear>

    <Add>
      <summary>Adds a variable to the collection.</summary>
      <param name="name">The name of the variable</param>
      <param name="value">The value of the variable</param>
      <remarks>Use this method to add a variable to the collection</remarks>
    </Add>

    <TryGetValue>
      <summary>Gets the value of a variable in the collection.</summary>
      <param name="name">The name of the variable</param>
      <param name="value">The location to store the value of the variable</param>
      <returns>True if the collection contains a variable with the given name; False otherwise</returns>
      <remarks>Use this method to get the value of a variable in the collection</remarks>
    </TryGetValue>

    <Remove>
      <summary>Removes a variable from the collection.</summary>
      <param name="name">The name of the variable</param>
      <remarks>Use this method to remove a variable from the collection</remarks>
    </Remove>

    <ContainsKey>
      <summary>Determines if the collection contains a variable.</summary>
      <param name="name">The name of the variable</param>
      <returns>True if the collection has a variable with the given name; False otherwise</returns>
      <remarks>Use this method to determine if the collection contains a variable</remarks>
    </ContainsKey>

    <Item>
      <summary>Gets or sets the value of a variable.</summary>
      <param name="name">The name of the variable</param>
      <returns>The value of the variable</returns>
      <remarks>
        Use this method to get or set the value of a variable.  If a variable with the given name does not exist, a new variable will be defined.  Otherwise, the
        value of the existing variable will be overwritten.
      </remarks>
      <exception cref="T:System.ArgumentException">The type of the new value is not compatible with the type of the existing variable</exception>
    </Item>

    <Count>
      <summary>Gets the number of variables defined in the collection.</summary>
      <returns>The number of variables in the collection</returns>
      <remarks>Use this method to get a count of the number of variables in the collection</remarks>
    </Count>

    <Keys>
      <summary>Gets a collection with the names of all variables.</summary>
      <returns>A collection with all the names</returns>
      <remarks>Use this method to access all the variable names in the collection</remarks>
    </Keys>

    <Values>
      <summary>Gets a collection with the values of all variables.</summary>
      <returns>A collection with all the values</returns>
      <remarks>Use this method to access all the variable values in the collection</remarks>
    </Values>

    <DefineVariable>
      <summary>
        Defines a variable with a specific type.
      </summary>
      <param name="name">The name of the variable</param>
      <param name="variableType">The type of the new variable</param>
      <remarks>Use this method when you want to add a variable with a type that is different than what would be inferred from defining it using the indexer.</remarks>
    </DefineVariable>

    <GetVariableValueInternal>
      <summary>Gets the value of a variable</summary>
      <typeparam name="T">The type of the variable's value</typeparam>
      <param name="name">The name of the variable</param>
      <returns>The variable's value</returns>
      <remarks>This method is used by the expression to retrieve the values of variables during evaluation.  It must be public so that all expressions
      can access it.  It is meant for internal use and you shouldn't depend on any of its functionality.</remarks>
    </GetVariableValueInternal>

    <GetFunctionResultInternal>
      <summary>Gets the result of an on-demand function</summary>
      <typeparam name="T">The type of the result's value</typeparam>
      <param name="name">The name of the function</param>
      <returns>The function's result</returns>
      <remarks>
        This method is used by the expression to retrieve the values of on-demand functions during evaluation.  It must be public so that all expressions
        can access it.  It is meant for internal use and you shouldn't depend on any of its functionality.
      </remarks>
    </GetFunctionResultInternal>

    <GetVirtualPropertyValueInternal>
      <summary>Gets the result of a virtual property</summary>
      <typeparam name="T">The type of the result's value</typeparam>
      <param name="name">The name of the property</param>
      <returns>The property's value</returns>
      <remarks>
        This method is used by the expression to retrieve the values of virtual properties during evaluation.  It must be public so that all expressions
        can access it.  It is meant for internal use and you shouldn't depend on any of its functionality.
      </remarks>
    </GetVirtualPropertyValueInternal>

    <GetVariableType>
      <summary>
        Gets the type of a variable.
      </summary>
      <param name="name">The name of the variable</param>
      <returns>The type of the variable's value</returns>
      <remarks>Use this method to get the type of the value of a variable.</remarks>
    </GetVariableType>

    <ResolveVariableType>
      <summary>
        Occurs when an expression needs the type of a variable.
      </summary>
      <remarks>This event is raised when an expression references a variable that doesn't exist in its variable collection.  You can handle this event to provide on-demand variables.</remarks>
    </ResolveVariableType>

    <ResolveVariableValue>
      <summary>
        Occurs when an expression needs the value of a variable.
      </summary>
      <remarks>This event is raised when an expression references a variable that doesn't exist in its variable collection.  You can handle this event to provide on-demand variables.</remarks>
    </ResolveVariableValue>

    <ResolveFunction>
      <summary>
        Occurs when an expression needs the return type of a function.
      </summary>
      <remarks>
        This event is raised when an expression references a function that doesn't exist on the expression owner or imports.  By handling this event and providing a value for the <see cref="P:Ciloci.Flee.ResolveFunctionEventArgs.ReturnType"/> property, you can implement an on-demand function.
      </remarks>
    </ResolveFunction>

    <InvokeFunction>
      <summary>
        Occurs when an expression needs the return value of a function.
      </summary>
      <remarks>
        This event is raised when an expression needs the return value of an on-demand function.  By handling this event and providing a value for the <see cref="P:Ciloci.Flee.InvokeFunctionEventArgs.Result"/> property, you can invoke your on-demand function.
      </remarks>
    </InvokeFunction>
  </VariableCollection>
  <CircularReferenceException>
    <Class>
      <summary>
        Represents the exception thrown when a circular reference is detected in the calculation engine.
      </summary>
      <remarks>
        This exception will be thrown when Recalculate is called on the CalculationEngine and there is a circular reference present.
      </remarks>
    </Class>
  </CircularReferenceException>
  <ResolveVariableTypeEventArgs>
    <Class>
      <summary>
        Provides the data for the ResolveVariableType event.
      </summary>
      <remarks>Use this class to provide the type of an on-demand variable.</remarks>
    </Class>

    <VariableName>
      <summary>
        Gets the name of an on-demand variable.
      </summary>
      <value>The name of the variable</value>
      <remarks>
        Use this property to get the name of the variable whose type needs to be resolved.
      </remarks>
    </VariableName>

    <VariableType>
      <summary>
        Gets or sets the type of an on-demand variable.
      </summary>
      <value>The type of the variable</value>
      <remarks>
        Use this property to get or set the type of the on-demand variable.
      </remarks>
    </VariableType>
  </ResolveVariableTypeEventArgs>
  <ResolveVariableValueEventArgs>
    <Class>
      <summary>
        Provides the data for the ResolveVariableValue event.
      </summary>
      <remarks>Use this class to provide the value of an on-demand variable.</remarks>
    </Class>

    <VariableName>
      <summary>
        Gets the name of an on-demand variable.
      </summary>
      <value>The name of the variable</value>
      <remarks>
        Use this property to get the name of the variable whose value needs to be resolved.
      </remarks>
    </VariableName>

    <VariableType>
      <summary>
        Gets the type of an on-demand variable.
      </summary>
      <value>The type of the variable</value>
      <remarks>
        Use this property to get the type of the variable whose value needs to be resolved.
      </remarks>
    </VariableType>

    <VariableValue>
      <summary>
        Gets or sets the value of an on-demand variable.
      </summary>
      <value>The value of the variable</value>
      <remarks>
        Use this property to get or set the value of an on-demand variable.
      </remarks>
    </VariableValue>
  </ResolveVariableValueEventArgs>
  <ResolveFunctionEventArgs>
    <Class>
      <summary>
        Provides the data for the ResolveFunction event.
      </summary>
      <remarks>Use this class to provide the return type of an on-demand function.</remarks>
    </Class>

    <FunctionName>
      <summary>
        Gets the name of the on-demand function being resolved.
      </summary>
      <value>The name of the function</value>
      <remarks>
        Use this property to get the name of the on-demand function being resolved.
      </remarks>
    </FunctionName>

    <ArgumentTypes>
      <summary>
        Gets the types of the arguments to the on-demand function being resolved.
      </summary>
      <value>An array with the type of each argument</value>
      <remarks>
        Use this property to get the types of the arguments to the on-demand function being resolved.
      </remarks>
    </ArgumentTypes>

    <ReturnType>
      <summary>
        Gets or sets the return type of the on-demand function being resolved.
      </summary>
      <value>The return type of the function</value>
      <remarks>
        Use this property to set the return type of the on-demand function being resolved.
      </remarks>
    </ReturnType>
  </ResolveFunctionEventArgs>
  <InvokeFunctionEventArgs>
    <Class>
      <summary>
        Provides the data for the InvokeFunction event.
      </summary>
      <remarks>Use this class to provide the return value of an on-demand function.</remarks>
    </Class>

    <FunctionName>
      <summary>
        Gets the name of the on-demand function being invoked.
      </summary>
      <value>The name of the function</value>
      <remarks>
        Use this property to get the name of the on-demand function being invoked.
      </remarks>
    </FunctionName>

    <Arguments>
      <summary>
        Gets the values of the arguments to the on-demand function being invoked.
      </summary>
      <value>An array with the values of each argument</value>
      <remarks>
        Use this property to get the values of the arguments to the on-demand function being invoked.
      </remarks>
    </Arguments>

    <Result>
      <summary>
        Gets or sets the result of the on-demand function being invoked.
      </summary>
      <value>The return value of the function</value>
      <remarks>
        Use this property to set the return value of the on-demand function being invoked.
      </remarks>
    </Result>
  </InvokeFunctionEventArgs>
  <ExpressionInfo>
    <Class>
      <summary>
        Holds information about a compiled expression.
      </summary>
      <remarks>
        This class holds information about an expression after it has been compiled.  For example: you can use this class to find out what variables
        an expression uses.
      </remarks>
    </Class>

    <GetReferencedVariables>
      <summary>
        Gets the variables that are used in an expression.
      </summary>
      <returns>A string array containing all the variables used in the expression.</returns>
      <remarks>Use this method when you need to get a list of all variables used in an expression.</remarks>
    </GetReferencedVariables>
  </ExpressionInfo>
  <ExpressionOwnerMemberAccessAttribute>
    <Class>
      <summary>
        Specifies whether access to a member on the expression owner is allowed.
      </summary>
      <remarks>
        Use this attribute to control the accessibility of individual members on the expression owner.  The access specified in
        this attribute overrides the access level specified using the <see cref="P:Ciloci.Flee.ExpressionOptions.OwnerMemberAccess"/> property.
      </remarks>
    </Class>

    <New>
      <summary>
        Initializes the attribute with the desired access.
      </summary>
      <param name="allowAccess">True to allow the member to be used in an expression;False otherwise</param>
      <remarks>Initializes the attribute with the desired access.</remarks>
    </New>
  </ExpressionOwnerMemberAccessAttribute>
  <IExpression>
    <Class>
      <summary>Interface implemented by all expressions</summary>
      <remarks>This is the base interface that exposes members common to both dynamic and generic expressions.</remarks>
    </Class>

    <Clone>
      <summary>
        Creates a clone of the current expression
      </summary>
      <returns>A copy of the current expression with its own set of variables</returns>
      <remarks>Use this method when you need to create a copy of an existing expression without the parsing/compilation overhead</remarks>
    </Clone>

    <Text>
      <summary>Gets the text the expression was created with</summary>
      <value>A string with the expression's text</value>
      <remarks>Use this property to get the text that was used to compile the expression.</remarks>
    </Text>

    <Info>
      <summary>
        Gets the expression's <see cref="T:Ciloci.Flee.ExpressionInfo"/> instance.
      </summary>
      <value>The ExpressionInfo instance.</value>
      <remarks>
        Use this property to access the expression's ExpressionInfo instance which holds information about the expression.
      </remarks>
    </Info>

    <Context>
      <summary>Gets the context the expression was created with</summary>
      <value>
        The expression's <see cref="T:Ciloci.Flee.ExpressionContext"/> instance
      </value>
      <remarks>Use this property to get the context that was used to compile the expression.</remarks>
    </Context>

    <Owner>
      <summary>Gets or sets the expression's owner</summary>
      <value>
        The expression's owner instance.  Must be of the same type as the expression's original owner.
      </value>
      <remarks>Use this property to get or set the instance of the expression's owner.</remarks>
    </Owner>
  </IExpression>
  <IDynamicExpression>
    <Class>
      <summary>Interface implemented by all expressions that evaluate to an Object</summary>
      <remarks>This is the interface that dynamic expressions must implement</remarks>
    </Class>

    <Evaluate>
      <summary>Evaluates the dynamic expression</summary>
      <returns>An Object instance that represents the result of evaluating the expression</returns>
      <remarks>Use this method to evaluate the expression.</remarks>
    </Evaluate>
  </IDynamicExpression>
  <IGenericExpression>
    <Class>
      <summary>Interface implemented by all expressions that evaluate to a specific type</summary>
      <typeparam name="T">The type that the expression will evaluate to</typeparam>
      <remarks>This is the interface that generic expressions must implement</remarks>
    </Class>

    <Evaluate>
      <summary>Evaluates the generic expression</summary>
      <returns>The result of evaluating the expression</returns>
      <remarks>Use this method to evaluate the expression.</remarks>
    </Evaluate>
  </IGenericExpression>
  <ExpressionContext>
    <Class>
      <summary>Class that holds all information required to create an expression</summary>
      <remarks>This class holds all information required to create an expression.</remarks>
      <threadsafety instance="true">The CompileDynamic and CompileGeneric methods are thread-safe.</threadsafety>
      <example>
        This example shows how to create an evaluate an expression:
        <code lang="C#">
// Define the context of our expression
ExpressionContext context = new ExpressionContext();
// Allow the expression to use all static public methods of System.Math
context.Imports.AddType(typeof(Math));

// Define an int variable
context.Variables["a"] = 100;

// Create a dynamic expression that evaluates to an Object
IDynamicExpression eDynamic = context.CompileDynamic("sqrt(a) + pi");
// Create a generic expression that evaluates to a double
IGenericExpression&lt;double&gt; eGeneric = context.CompileGeneric&lt;double&gt;("sqrt(a) + pi");

// Evaluate the expressions
double result = (double)eDynamic.Evaluate();
result = eGeneric.Evaluate();

// Update the value of our variable
context.Variables["a"] = 144;
// Evaluate again to get the updated result
result = eGeneric.Evaluate();</code>
      </example>
    </Class>

    <New1>
      <summary>Creates a new expression context with the default expression owner.</summary>
      <remarks>Use this constructor to create an expression context when you don't plan to use an expression owner.</remarks>
    </New1>

    <New2>
      <summary>Creates a new expression context with a specified expression owner.</summary>
      <param name="expressionOwner">The expression owner instance to use</param>
      <remarks>Use this constructor to create an expression context when you want to supply an expression owner.</remarks>
    </New2>

    <CompileDynamic>
      <summary>Creates a dynamic expression (one that evaluates to Object) from an expression text string and the current context</summary>
      <param name="expression">The expression text to parse</param>
      <returns>A new dynamic expression</returns>
      <remarks>
        Use this method when you want to create an expression that evaluates to an Object.  "Dynamic" means that the result type
        of the expression can be anything and is not fixed as with a generic expression.
        <note>
        The context, imports, and options of the compiled expression will be a clone of the originals.  The variables however
        are not cloned and will point to the same instance.
        </note>
      </remarks>
      <exception cref="T:Ciloci.Flee.ExpressionCompileException">The expression could not be compiled</exception>
    </CompileDynamic>

    <CompileGeneric>
      <summary>Creates a generic expression from an expression text string and the current context</summary>
      <typeparam name="TResultType">The type that the expression evaluates to</typeparam>
      <param name="expression">The expression text to parse</param>
      <returns>A new generic expression</returns>
      <remarks>
        Use this method when you want to create an expression that evaluates to a strongly-typed value.
        <note>
        The context, imports, and options of the compiled expression will be a clone of the originals.  The variables however
        are not cloned and will point to the same instance.
        </note>
      </remarks>      
      <exception cref="T:Ciloci.Flee.ExpressionCompileException">The expression could not be compiled</exception>
    </CompileGeneric>

    <Variables>
      <summary>Gets the variables available to an expression</summary>
      <value>The VariableCollection instance</value>
      <remarks>Use this property to get collection of variables available to an expression.</remarks>
    </Variables>

    <CalculationEngine>
      <summary>Gets the CalculationEngine instance used by the expression.</summary>
      <value>The CalculationEngine instance</value>
      <remarks>Use this property to get CalculationEngine instance used by an expression</remarks>
    </CalculationEngine>

    <Imports>
      <summary>
        Gets the types imported by an expression.
      </summary>
      <value>The collection of imported types.</value>
      <remarks>
        Use this property to get the imports that will be used by an expression.<seealso cref="T:Ciloci.Flee.ImportsCollection"/>
      </remarks>
    </Imports>

    <Clone>
      <summary>Creates a copy of the current context.</summary>
      <returns>An identical copy of the current context</returns>
      <remarks>Use this method when you need an indentical copy of an existing context.  Note that</remarks>
    </Clone>

    <Options>
      <summary>Gets the ExpressionOptions to be used in an expression</summary>
      <value>The ExpressionOptions instance</value>
      <remarks>Use this property to access the options to be used in an expression.</remarks>
    </Options>
    <ParserOptions>
      <summary>Gets the ExpressionParserOptions for this context.</summary>
      <value>The ExpressionParserOptions instance</value>
      <remarks>Use this property to customize the expression parser.</remarks>
    </ParserOptions>
  </ExpressionContext>

  <Member name="BatchLoadCompileException">
    <summary>
      The exception thrown when an batch loaded expression cannot be compiled.
    </summary>
    <remarks>
      This exception is thrown whenever a batch loaded expression cannot be compiled.  You use the AtomName and ExpressionText properties 
      to get more information about the source of the exception.      
    </remarks>
  </Member>

  <Member name="BatchLoadCompileException.AtomName">
    <summary>
      Gets the name of the expression that could not be compiled.
    </summary>
    <value>The name of the expression</value>
    <remarks>Use this property to determine the name of the expression that caused the exception.</remarks>
  </Member>

  <Member name="BatchLoadCompileException.ExpressionText">
    <summary>
      Gets the text of the expression that could not be compiled.
    </summary>
    <value>The text of the expression</value>
    <remarks>Use this property to determine the text of the expression that caused the exception.</remarks>
  </Member>
  
  <Member name="BatchLoader">
    <summary>
      Represents a class that can be used to populate the calculation engine in one batch.
    </summary>
    <remarks>
      Normally, you have to add an expression to the calculation engine after any expressions it depends on.  By using this class, you can load expressions in any order and
      then have them be loaded into the calculation engine in one call.
    </remarks>
  </Member>

  <Member name="BatchLoader.Add">
    <summary>
      Adds an expression to the batch loader.
    </summary>
    <param name="atomName">The name that the expression will be associated with</param>
    <param name="expression">The expression to add</param>
    <param name="context">The context for the expression</param>
    <remarks>Use this method to add an expression to the batch loader and associate it with a name</remarks>
  </Member>

  <Member name="BatchLoader.Contains">
    <summary>
      Determines if the loader contains an expression with a given name.
    </summary>
    <param name="name">The name of the expression to look up</param>
    <returns>True if the loader has an expression with the name; False otherwise</returns>
    <remarks>
      Use this method to determine if the loader contains an expression with a given name.
    </remarks>
  </Member>

  <Member name="CalculationEngine">
  <summary>
    Creates a calculation network which allows expressions to refer to the result of other expressions, tracks dependencies, and enables natural order recalculation.
  </summary>
  <remarks>
  This class acts as a container for expressions.  As expressions are added to it, their dependencies are tracked and their result is cached.  Expressions defined
  in this class can reference other contained expressions.  Once all expressions are added, a natural order recalculate can be performed.
  </remarks>
  <example>This example shows how to add expressions to the engine and have them reference other contained expressions:
  <code lang="C#">
ExpressionContext context = new ExpressionContext();
VariableCollection variables = context.Variables;

// Add some variables
variables.Add("x", 100);
variables.Add("y", 200);

// Add an expression to the calculation engine as "a"
engine.Add("a", "x * 2", context);

// Add an expression to the engine as "b"
engine.Add("b", "y + 100", context);

// Add an expression at "c" that uses the results of "a" and "b"
// Notice that we have to prefix the expression names with a '$'
engine.Add("c", "$a + $b", context);

// Get the value of "c"
int result = engine.GetResult&lt;int&gt;("c");

// Update a variable on the "a" expression
variables["x"] = 200;

// Recalculate it
engine.Recalculate("a");

// Get the updated result
result = engine.GetResult&lt;int&gt;("c");
      </code>
  </example>
</Member>

<Member name="CalculationEngine.NodeRecalculated">
  <summary>
  Occurs when the calculation engine recalculates a node.
  </summary>
  <remarks>You can listen to this event to be notified when a node in the calculation engine is recalculated.</remarks>
</Member>

<Member name="CalculationEngine.Clear">
  <summary>
  Clears all expressions from the CalculationEngine
  </summary>    
  <remarks>Use this method to reset the CalculationEngine to the empty state.</remarks>
</Member>

<Member name="CalculationEngine.DependencyGraph">
  <summary>
  Gets a string representation of the engine's dependency graph.    
  </summary>
  <value>A string representing the graph.</value>
  <remarks>Use this property to get a string version of the engine's dependency graph.  There will be one line for each dependency. Each line will
  be of the format "[reference] -> [dependant1, dependant2]" and is read as "A change in [reference] will cause a change in [dependant]".</remarks>
</Member>

<Member name="CalculationEngine.Count">
  <summary>
  Gets the number of expressions contained in the calculation engine.
  </summary>
  <value>The number of expressions in the calculation engine</value>
  <remarks>
  Use this property to see how many expressions are contained in the calculation engine.
  </remarks>
</Member>

<Member name="CalculationEngine.GetResult">
  <summary>
    Gets the cached result of a contained expression.
  </summary>
  <typeparam name="T">The type of the expression's result.</typeparam>
  <param name="name">The name that the expression is associated with</param>
  <returns>The cached result of evaluating the expression</returns>
  <remarks>Use this method after a recalculate to get the updated value of an expression.</remarks>
</Member>

<Member name="CalculationEngine.GetResult2">
  <summary>
  Gets the cached result of a contained expression.
  </summary>    
  <param name="name">The name that the expression is associated with</param>
  <returns>The cached result of evaluating the expression</returns>
  <remarks>Use this method after a recalculate to get the updated value of an expression.</remarks>
</Member>

<Member name="CalculationEngine.GetExpression">
  <summary>
  Gets the expression associated with a name.
  </summary>    
  <param name="name">The name that the expression is associated with</param>
  <returns>The expression associated with the given name</returns>
  <remarks>Use this method to obtain the expression associated with a name.</remarks>
</Member>

<Member name="CalculationEngine.GetDependents">
  <summary>
  Gets the names of the expressions that depend on a given name.
  </summary>
  <param name="name">The name of the expression to look up</param>
  <returns>The names of all expressions that depend on the expression with the given name</returns>
  <remarks>Use this method to obtain all the expressions that depend on a given name.  For example: if a=100, b=200, and c=a+b, then calling 
  GetDependents("a") will return "c" since when the value of "a" changes, the value of "c" will also change.  This method is not recursive, so it
  will only return the names that directly depend on the given name.  This method is the inverse of <see cref="M:Ciloci.Flee.CalculationEngine.GetPrecedents(System.String)"/>
  </remarks>
</Member>

<Member name="CalculationEngine.GetPrecedents">
  <summary>
  Gets the names of the expressions that a given name depends on.
  </summary>
  <param name="name">The name of the expression to look up</param>
  <returns>The names of all expressions that the given name depends on</returns>
  <remarks>
    Use this method to obtain all the expressions that a given name depends on.  For example: if a=100, b=200, and c=a+b, then calling
    GetPrecedents("c") will return "a, b" since when either "a" or "b" change, the value of "c" will also change.  This method is not recursive, so it
    will only return the names that the given name directly depends on.    This method is the inverse of <see cref="M:Ciloci.Flee.CalculationEngine.GetDependents(System.String)"/>
  </remarks>
</Member>

<Member name="CalculationEngine.HasDependents">
  <summary>
  Determines if an expression with a given name is referenced by any other expressions in the engine.
  </summary>
  <param name="name">The name of the expression to look up</param>
  <returns>True if the engine has expressions that depend on it; False otherwise</returns>
  <remarks>
  Use this method to determine if the expression associated with a given name has any expressions that depend on it.  For example: you can use this method to allow 
  a user to remove an expression only when no other expressions depend on it.
  </remarks>
</Member>

  <Member name="CalculationEngine.HasPrecedents">
    <summary>
      Determines if an expression with a given name depends on any other expression.
    </summary>
    <param name="name">The name of the expression to look up</param>
    <returns>True if the name has expressions that it depends on; False otherwise</returns>
    <remarks>
      Use this method to determine if an expression depends on any other expressions.
    </remarks>
  </Member>

  <Member name="CalculationEngine.Contains">
    <summary>
      Determines if the engine contains an expression with a given name.
    </summary>
    <param name="name">The name of the expression to look up</param>
    <returns>True if the engine has an expression with the name; False otherwise</returns>
    <remarks>
      Use this method to determine if the calculation engine contains an expression with a given name.
    </remarks>
  </Member>

<Member name="CalculationEngine.Recalculate">
  <summary>
  Performs a natural order recalculation of the engine.
  </summary>
  <param name="roots">The names representing the starting points of the recalculation</param>    
  <remarks>This method will perform a natural order recalculate on the expressions in the engine.  The recalculation will start at the given roots
  and continue with all their dependents.  If no roots are given, then a recalculation of all expressions is performed.</remarks>
  <exception cref="T:Ciloci.Flee.CircularReferenceException">A recalculate is requested on an engine containing a circular reference</exception>
</Member>

  <Member name="CalculationEngine.Add">
    <summary>
      Adds an expression to the calculation engine.
    </summary>
    <param name="atomName">The name that the expression will be associated with</param>
    <param name="expression">The expression to add</param>
    <param name="context">The context for the expression</param>
    <remarks>Use this method to add an expression to the engine and associate it with a name</remarks>
  </Member>

<Member name="CalculationEngine.Remove">
  <summary>
  Removes an expression and all its dependents from the calculation engine.
  </summary>
  <param name="name">The name whose expression to remove</param>
  <returns>True if the name was removed from the engine; False otherwise</returns>
  <remarks>Use this method to remove an expression and all its dependents from the calculation engine.  No exception is thrown if the
  name does not exist in the engine.</remarks>
</Member>

  <Member name="CalculationEngine.CreateBatchLoader">
    <summary>
      Creates a BatchLoader that can be used to populate the calculation engine in one batch.
    </summary>
    <returns>A new instance of the BatchLoader class</returns>
    <remarks>
      Use this method to create a BatchLoader instance.
    </remarks>
  </Member>

  <Member name="CalculationEngine.BatchLoad">
    <summary>
      Populates the calculation engine from the given BatchLoader.
    </summary>
    <param name="loader">The batch loader instance to use</param>
    <remarks>
      Call this method to load the calculation engine with all the expressions in the given batch loader.
    </remarks>
  </Member>

<Member name="NodeEventArgs">
  <summary>
  Provides the data for the NodeRecalculated event.
  </summary>
  <remarks>Use the members of this class to get additional information about the recalculated node.</remarks>
</Member>

<Member name="NodeEventArgs.Name">
  <summary>
    Gets the name of the recalculated node.
  </summary>
  <value>The name of the node</value>
  <remarks>
    Use this property to get the name of the recalculated node.
  </remarks>
</Member>

<Member name="NodeEventArgs.Result">
  <summary>
    Gets the recalculated result of the node.
  </summary>
  <value>The value of the result</value>
  <remarks>
    Use this property to get the recalculated result of the node.
  </remarks>
</Member>  
</DocComments>