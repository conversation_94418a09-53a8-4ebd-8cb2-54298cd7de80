//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace VZoneTrack.Gps.V32.WebApi
{
    using System;
    using System.Collections.Generic;
    
    public partial class Device
    {
        public long ID { get; set; }
        public string DeviceIMEI { get; set; }
        public Nullable<System.DateTime> ProgrammedOn { get; set; }
        public string DeviceType { get; set; }
        public string Description { get; set; }
        public Nullable<int> TimeZone { get; set; }
        public Nullable<int> MCC { get; set; }
        public Nullable<int> MNC { get; set; }
        public string SIMNO { get; set; }
        public string SIMIMSI { get; set; }
        public Nullable<int> CountryCode { get; set; }
        public bool IsActive { get; set; }
        public bool IsDelete { get; set; }
        public Nullable<long> CreatedBy { get; set; }
        public System.DateTime CreatedDate { get; set; }
        public Nullable<long> ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public short Ign_Port { get; set; }
        public Nullable<bool> IsTraceDataRequired { get; set; }
        public int TypeId { get; set; }
        public string BatchNo { get; set; }
        public Nullable<System.DateTime> ExpiryDate { get; set; }
        public Nullable<int> AccessKey { get; set; }
    }
}
