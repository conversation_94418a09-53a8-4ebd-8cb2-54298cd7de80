//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace VZoneTrack.Gps.V32.WebApi
{
    using System;
    using System.Collections.Generic;
    
    public partial class User
    {
        public long ID { get; set; }
        public long CompanyID { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }
        public short IsAdmin { get; set; }
        public Nullable<System.DateTime> LastLoggedIn { get; set; }
        public int TotalLogins { get; set; }
        public bool IsSystemDefined { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public Nullable<long> CreatedBy { get; set; }
        public Nullable<System.DateTime> CreatedDate { get; set; }
        public Nullable<long> ModifiedBy { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<bool> IsTrialAccount { get; set; }
        public Nullable<System.DateTime> TrialExpiry { get; set; }
        public Nullable<System.DateTime> Alerts_ReadTime { get; set; }
        public bool IsOnline { get; set; }
        public string UserImage { get; set; }
        public string Email { get; set; }
        public bool IsForceRebootRequired { get; set; }
        public Nullable<short> RebootRequestType { get; set; }
        public string User_Email { get; set; }
        public string MobileNo { get; set; }
        public string Department { get; set; }
        public string Designation { get; set; }
        public string OfficeLocation { get; set; }
        public string Remarks { get; set; }
        public bool IsProfileUpdated { get; set; }
        public Nullable<System.DateTime> ProfileUpdatedOn { get; set; }
        public bool IsTrainingRequired { get; set; }
        public Nullable<System.DateTime> TrainingGivenOn { get; set; }
        public bool IsMentor { get; set; }
        public bool IsEmailVerified { get; set; }
        public bool IsStalledNotificationReqd { get; set; }
        public Nullable<bool> HasWebApiAccess { get; set; }
        public string AuthenticationLogging { get; set; }
        public bool IsRememberMeEnabled { get; set; }
        public Nullable<System.DateTime> RememberMeSetOn { get; set; }
        public bool IsAsateelInfoUpdated { get; set; }
        public string SecretKey { get; set; }
    }
}
