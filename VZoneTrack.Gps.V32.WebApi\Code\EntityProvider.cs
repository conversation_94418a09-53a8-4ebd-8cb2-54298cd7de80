﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using VZoneTrack.Gps.V32.WebApi.Models;

namespace VZoneTrack.Gps.V32.WebApi.Code
{
    public static class EntityProvider
    {
        public static int GetUserIdFromUsername(string userName)
        {
            int _userId = 0;
            User user = null;

            using (var context = new VZoneTrackEntities())
            {
                var query = from usr in context.Users
                            where usr.UserName.ToUpper() == userName.ToUpper()
                            select usr;

                user = query.FirstOrDefault<User>();
                _userId = (int)user.ID;
            }
            return _userId;
        }


        public static long GetCompanyIdFromUsername(string userName)
        {
            long _companyId = 0;
            User user = null;

            using (var context = new VZoneTrackEntities())
            {
                var query = from usr in context.Users
                            where usr.UserName.ToUpper() == userName.ToUpper()
                            select usr;

                user = query.FirstOrDefault<User>();
                _companyId = user.CompanyID;
            }
            return _companyId;
        }

        public static int GetApiRateLimit(string userName)
        {
            int _rateLimitFrequency = 0;

             Company company = null;

            using (var context = new VZoneTrackEntities())
            {
                var query = from usr in context.Users
                            join cmp in context.Company on 
                            usr.CompanyID equals cmp.ID
                            where usr.UserName.ToUpper() == userName.ToUpper()
                            select cmp;

                company = query.FirstOrDefault<Company>();
                _rateLimitFrequency = company.ApiRateLimit;
                
                //if(_rateLimitFrequency.Equals(0))
                //{
                //    _rateLimitFrequency = Convert.ToInt32(ConfigurationManager.AppSettings["RATE_LIMIT_FREQUENCY"].ToString());
                //}
            }
            return _rateLimitFrequency;
        }

        public static Company GetCompanyInfoFromAccessCode(string AccessCode)
        {
            Company company = null;
            using (var context = new VZoneTrackEntities())
            {
                var query = from com in context.Company
                            where com.Code == AccessCode
                            select com;

                company = query.FirstOrDefault<Company>();
            }
            return company;
        }
        public static Vehicle GetVehicleInfoFromAssetCode(string AssetCode)
        {
            Vehicle vehicle = null;

            using (var context = new VZoneTrackEntities())
            {
                var query = from veh in context.Vehicle
                            where veh.AssetCode == AssetCode
                            select veh;

                vehicle = query.FirstOrDefault<Vehicle>();
            }
            return vehicle;
        }
        public static Vehicle GetVehicleInfoFromVehicleId(long vehicleId)
        {
            Vehicle vehicle = null;

            using (var context = new VZoneTrackEntities())
            {
                var query = from veh in context.Vehicle
                            where veh.ID == vehicleId
                            select veh;

                vehicle = query.FirstOrDefault<Vehicle>();
            }
            return vehicle;
        }
        public static long GetCompanyIdFromAccessCode(string AccessCode)
        {
            long _companyId = 0;
            Company company = null;

            using (var context = new VZoneTrackEntities())
            {
                var query = from com in context.Company
                            where com.Code == AccessCode
                            select com;

                company = query.FirstOrDefault<Company>();
            }
            _companyId = company.ID;
            return _companyId;
        }

        public static long GetVehicleIdFromAssetCode(string AssetCode)
        {
            long _vehicleId = 0;
            Vehicle vehicle = null;

            using (var context = new VZoneTrackEntities())
            {
                var query = from veh in context.Vehicle
                            where veh.AssetCode == AssetCode
                            select veh;

                vehicle = query.FirstOrDefault<Vehicle>();
            }
            _vehicleId = vehicle.ID;
            return _vehicleId;
        }

        public static List<VehicleInfo> GetVehicleInfo(long companyId,string AssetCode = "")
        {
            List<VehicleInfo> lstVehicleInfo = new List<VehicleInfo>();

            using (VZoneTrackEntities context = new VZoneTrackEntities())
            {
                List<VehiclesInfoComplex> vehicleInfoDetails = context.GetVehiclesDetails(companyId, AssetCode).ToList();

                foreach (VehiclesInfoComplex vehInfo in vehicleInfoDetails)
                {
                    VehicleInfo _vehicleInfo = new VehicleInfo
                    {
                        VehicleId = Convert.ToInt32(vehInfo.VehicleId),
                        AssetCode = vehInfo.AssetCode,
                        AllowedSpeed = vehInfo.MaxSpeed.ToString() + "kmph",
                        Category = vehInfo.CategoryName,
                        Color = vehInfo.Color,
                        DisplayText = vehInfo.DisplayText,
                        FuelType = vehInfo.FuelType,
                        GPSInstallationDate = Convert.ToDateTime(vehInfo.InstallationDate).ToString("dd MMM yyyy"),
                        Model = vehInfo.Model,
                        MSRP = vehInfo.MSRP > 0 ? vehInfo.MSRP.ToString() + "AED" : "",
                        PlateNo = vehInfo.PlateNo,
                        TankCapcity = vehInfo.FuelTankCapacity > 0 ? vehInfo.FuelTankCapacity.ToString() + "L" : "",
                        Trim = vehInfo.VehicleTrim,
                        VIN = vehInfo.VIN,
                        YOM = vehInfo.ManufactureYear > 0 ? vehInfo.ManufactureYear.ToString() : ""
                    };

                    Accessories _accessories = new Accessories
                    {
                        Authentication = Convert.ToBoolean(vehInfo.IsRFIDEnabled),
                        Buzzer = Convert.ToBoolean(vehInfo.IsBuzzerEnabled),
                        CANBUS = Convert.ToBoolean(vehInfo.IsCANBUSEnabled),
                        Chiller = Convert.ToBoolean(vehInfo.IsChillerStatusEnabled),
                        Door = Convert.ToBoolean(vehInfo.IsDoorSensorEnabled),
                        Gadget = Convert.ToBoolean(vehInfo.IsGadgetInstalled),
                        Immobilization = Convert.ToBoolean(vehInfo.IsImmobilizationEnabled),
                        Seatbelt = Convert.ToBoolean(vehInfo.IsSeatbelt),
                        Temperature = Convert.ToBoolean(vehInfo.IsAnyTemperatureEnabled)
                    };
                    _vehicleInfo.Accessories = _accessories;

                    lstVehicleInfo.Add(_vehicleInfo);
                }
            }

            return lstVehicleInfo;
        }

        public static DateTime CurrentVehicleTime(int VehicleId)
        {
            DateTime? currentDateTime = null;
            using (var context = new VZoneTrackEntities())
            {
                int? _timeZone = context.usp_V32GetVehicleTimeZone(VehicleId).FirstOrDefault<int?>();
                currentDateTime = DateTime.UtcNow.AddMinutes(Convert.ToDouble(_timeZone));
            }
            return Convert.ToDateTime(currentDateTime);
        }

        public static string GetDriverCardNumber(long DriverId)
        {
            string  _cardNumber = string.Empty;
            V32DriverIDCard v32DriverIDCard = null;

            using (var context = new VZoneTrackEntities())
            {
                var query = from drv in context.Driver
                            join dic in context.V32DriverIDCard on
                            drv.DriverCardID equals dic.DriverCardID
                            where drv.ID == DriverId
                            select dic;

                v32DriverIDCard = query.FirstOrDefault<V32DriverIDCard>();
            }
            if (v32DriverIDCard != null)
                _cardNumber = v32DriverIDCard.CardNumber;

            return _cardNumber;
        }

        public static Personnel GetPersonnelInfo(long DriverId)
        {
            Personnel personnel = null;
            using (var context = new VZoneTrackEntities())
            {
                var query = from drv in context.Driver
                            join dic in context.V32DriverIDCard on
                            drv.DriverCardID equals dic.DriverCardID
                            where drv.ID == DriverId
                            select new Personnel
                            {
                                Name = drv.DriverName,
                                Code = string.IsNullOrEmpty(drv.Code) ? string.Empty : drv.Code,
                                CardNumber = dic.CardNumber
                            };

                personnel = query.FirstOrDefault<Personnel>();
            }
            return personnel;
        }
        public static Personnel GetPersonnelInfo(string CardNumber)
        {
            Personnel personnel = null;
            using (var context = new VZoneTrackEntities())
            {
                var query = from drv in context.Driver
                            join dic in context.V32DriverIDCard on
                            drv.DriverCardID equals dic.DriverCardID
                            where dic.CardNumber == CardNumber && drv.IsActive == true
                            select new Personnel
                            {
                                Name = drv.DriverName,
                                Code = string.IsNullOrEmpty(drv.Code) ? string.Empty: drv.Code,
                                CardNumber = dic.CardNumber
                            };

                personnel = query.FirstOrDefault<Personnel>();
            }
            return personnel;
        }

        public static int AddVehicle(JobInfo jobInfo)
        {
            using (var context = new VZoneTrackEntities())
            {
                var customerName = new SqlParameter("@in_CustomerName", jobInfo.CustomerName);
                var imei = new SqlParameter("@in_Imei", jobInfo.IMEI);
                var regNo = new SqlParameter("@in_RegNo", jobInfo.RegistrationNo);
                var vehicleType = new SqlParameter("@in_VehicleType", jobInfo.VehicleType);
                var make = new SqlParameter("@in_Make", jobInfo.Make);
                var model = new SqlParameter("@in_Model", jobInfo.Model);
                var odometer = new SqlParameter("@in_Odometer", jobInfo.Odometer);
                var accessories = new SqlParameter("@in_Accessories", jobInfo.Accessories);
                var ibuttonreadercount = new SqlParameter("@in_IButtonReaderCount", jobInfo.IButtonReaderCount);
                var buzzercount = new SqlParameter("@in_BuzzerCount", jobInfo.BuzzerCount);
                var rfidcount = new SqlParameter("@in_RFIDCount", jobInfo.RFIDCount);
                var dateofjob = new SqlParameter("@in_DateOfJob", jobInfo.DateOfJob);

                var result = context.Database.SqlQuery<int>(
                    "[dbo].[usp_AddVehicle_VZoneAutomation] @in_CustomerName," +
                    "                                       @in_Imei," +
                    "                                       @in_RegNo," +
                    "                                       @in_VehicleType," +
                    "                                       @in_Make," +
                    "                                       @in_Model," +
                    "                                       @in_Odometer," +
                    "                                       @in_Accessories," +
                    "                                       @in_IButtonReaderCount," +
                    "                                       @in_BuzzerCount," +
                    "                                       @in_RFIDCount," +
                    "                                       @in_DateOfJob",
                    customerName, imei, regNo, vehicleType, make, model, odometer, accessories, ibuttonreadercount, buzzercount, rfidcount, dateofjob
                ).SingleOrDefault();

                return result;
            }           
        }

        public static int AddDevice(DeviceInfo deviceInfo)
        {
            using (var context = new VZoneTrackEntities())
            {   
                var imei = new SqlParameter("@in_Imei", deviceInfo.IMEI);
                var programmedOn = new SqlParameter("@in_ProgrammedOn", deviceInfo.ProgrammedOn);
                var type = new SqlParameter("@in_Type", deviceInfo.Type);
                var sim = new SqlParameter("@in_Sim", deviceInfo.SIM);
                var simiccid = new SqlParameter("@in_SimICCID", deviceInfo.SIMICCID);


                var result = context.Database.SqlQuery<int>(
                    "[dbo].[usp_AddDevice_VZoneAutomation]  @in_Imei," +
                    "                                       @in_ProgrammedOn," +
                    "                                       @in_Type," +
                    "                                       @in_Sim," +
                    "                                       @in_SimICCID",
                    imei, programmedOn, type, sim, simiccid
                ).SingleOrDefault();

                return result;
            }
        }

        public static List<VehicleInfo> GetUserAuthorizedVehicles(long companyId, int userId)
        {
            using (var context = new VZoneTrackEntities())
            {
                // Check if user is Administrator
                bool isAdministrator = context.Users
                    .Where(u => u.ID == userId)
                    .Select(u => u.IsAdmin.Equals(1))
                    .FirstOrDefault();

                List<VehiclesInfoComplex> vehicleInfoDetails = context.GetVehiclesDetails(companyId, string.Empty).ToList();

                List<VehiclesInfoComplex> authorizedVehicles;

                if (isAdministrator)
                {   
                    authorizedVehicles = vehicleInfoDetails;
                }
                else
                {   
                    var authorizedVehicleIds = (from vg in context.VehicleGroup
                                                join ug in context.V32UserGroup on vg.GroupID equals ug.GroupId
                                                where ug.UserId == userId
                                                select vg.VehicleID)
                                                .Distinct()
                                                .ToList();

                    authorizedVehicles = vehicleInfoDetails
                                        .Where(v => v.VehicleId.HasValue && authorizedVehicleIds.Contains(v.VehicleId.Value))
                                        .ToList();
                }
                
                var result = authorizedVehicles.Select(v => new VehicleInfo
                {
                    VehicleId = v.VehicleId.HasValue ? (int)v.VehicleId.Value : 0,
                    AssetCode = v.AssetCode,
                    Category = v.CategoryName,
                    DisplayText = v.DisplayText,
                    PlateNo = v.PlateNo,
                    Model = v.Model,
                    FuelType = v.FuelType,
                    TankCapcity = v.FuelTankCapacity.HasValue ? v.FuelTankCapacity.Value.ToString("0.##") : "N/A",
                    AllowedSpeed = v.MaxSpeed.HasValue ? v.MaxSpeed.Value.ToString("0.##") : "N/A",
                    GPSInstallationDate = v.InstallationDate.HasValue ? v.InstallationDate.Value.ToString("yyyy-MM-dd") : "N/A",
                    VIN = v.VIN,
                    YOM = v.ManufactureYear.HasValue ? v.ManufactureYear.Value.ToString() : "N/A",
                    Trim = v.VehicleTrim,
                    Color = v.Color,
                    MSRP = v.MSRP.HasValue ? v.MSRP.Value.ToString("C") : "N/A",
                    Accessories = new Accessories
                    {
                        Authentication = v.IsRFIDEnabled ?? false,
                        Buzzer = v.IsBuzzerEnabled ?? false,
                        Immobilization = v.IsImmobilizationEnabled ?? false,
                        CANBUS = v.IsCANBUSEnabled ?? false,
                        Chiller = v.IsChillerStatusEnabled ?? false,
                        Door = v.IsDoorSensorEnabled ?? false,
                        Gadget = v.IsGadgetInstalled ?? false,
                        Seatbelt = v.IsSeatbelt ?? false,
                        Temperature = v.IsAnyTemperatureEnabled ?? false
                    }
                }).ToList();

                return result;
            }
        }
        public static List<VehicleBasicInfo> GetVehicleGroups(long vehicleId)
        {
            using (var context = new VZoneTrackEntities())
            {
                var result = (from v in context.Vehicle
                              where v.ID == vehicleId
                              select new VehicleBasicInfo
                              {   VehicleId = (int)v.ID,
                                  PlateNo = v.RegistrationNo,
                                  DisplayText = v.DisplayText,
                                  Groups = (from vg in context.VehicleGroup
                                            join g in context.Group on vg.GroupID equals g.ID
                                            where vg.VehicleID == v.ID
                                            select new GroupInfo
                                            {   GroupId = g.ID,
                                                GroupName = g.GroupName,
                                                IsSystemDefined = g.IsSystemDefined
                                            }).ToList()
                              }).ToList();

                return result;
            }
        }
    }
}