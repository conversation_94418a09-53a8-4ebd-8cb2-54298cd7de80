﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace VZoneTrack.Gps.V32.WebApi.Models
{
    public class TripSummaryApiResponse
    {
        public long CompanyId { get; set; }
        public List<VehicleTripSummary> Vehicle { get; set; }
    }

    public class VehicleTripSummary
    {
        public int VehicleId { get; set; }
        public string DisplayText { get; set; }
        public string AssetCode { get; set; }
        public List<TripData> Trips { get; set; }
    }

    public class TripData
    {
        public DateTime Date { get; set; }
        public TripSummary TripSummary { get; set; }
    }

    public class TripSummary
    {
        public double TopSpeed { get; set; }
        public double AvgSpeed { get; set; }
        public int TravelDurationMin { get; set; }
        public int IdleDurationMin { get; set; }
        public int StopDurationMin { get; set; }
        public int TotalDurationMin { get; set; }
        public double FuelCons { get; set; }
        public double FuelCost { get; set; }
        public int IdleViolationDurationMin { get; set; }
        public double IdleViolationFuelCons { get; set; }
        public double IdleViolationFuelCost { get; set; }
        public double DistanceTraveled { get; set; }
        public object AlertSummaryJson { get; set; }
        public object DoorSummaryJson { get; set; }
        public object TemperatureSummaryJson { get; set; }
        public object SwipeSummaryJson { get; set; }
        public object ChillerSummaryJson { get; set; }
    }

}