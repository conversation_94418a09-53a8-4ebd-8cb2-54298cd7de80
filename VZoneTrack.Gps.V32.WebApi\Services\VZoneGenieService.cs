﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using System.Data.Entity;
using log4net;
using VZoneTrack.Gps.V32.WebApi.Models;
using VZoneTrack.Gps.V32.WebApi.Interfaces;
using Newtonsoft.Json;
using System.Net.Http;
using VZoneTrack.Gps.V32.WebApi.Extensions;
using System.Data;
using VZoneTrack.Gps.V32.Business;
using VZoneTrack.Gps.V32.WebApi.Code;
using VZoneTrack.Gps.V32.WebApi.Helpers;
using System.Data.Common;

namespace VZoneTrack.Gps.V32.WebApi.Services
{
    public class VZoneGenieService : IVZoneGenieService
    {
        private readonly VZoneTrackEntities _dbContext;
        private static readonly ILog _logger = LogManager.GetLogger(typeof(VZoneGenieService));
        private static readonly string _microserviceBaseUrl = ConfigurationManager.AppSettings["MICROSERVICE_BASE_URL"];        

        public VZoneGenieService()
        {
            _dbContext = new VZoneTrackEntities();
        }

        public async Task<T> GetRawDataAsync<T>(string columnName, DateTime? from, DateTime? to, int? vehicleId, long companyId)
        {
            try
            {
                if ((to.HasValue && to.Value.Date >= DateTime.Today) || (!to.HasValue && !from.HasValue) || (!to.HasValue && from.HasValue && from.Value.Date >= DateTime.Today))
                {
                    var apiResult = await GetRawDataFromApiAsync<T>(columnName, vehicleId, companyId);
                    if (apiResult != null)
                    {
                        return apiResult;
                    }
                }

                var sql = $"SELECT TOP 1 [{columnName}] FROM AnalyticsTripSummary WITH(NOLOCK) WHERE CompanyId = @CompanyId";
                var parameters = new List<SqlParameter>
                {
                    new SqlParameter("@CompanyId", companyId)
                };

                if (vehicleId.HasValue)
                {
                    sql += " AND VehicleId = @VehicleId";
                    parameters.Add(new SqlParameter("@VehicleId", vehicleId.Value));
                }

                if (from.HasValue)
                {
                    sql += " AND TripDate >= @FromDate";
                    parameters.Add(new SqlParameter("@FromDate", from.Value));
                }

                if (to.HasValue)
                {
                    sql += " AND TripDate <= @ToDate";
                    parameters.Add(new SqlParameter("@ToDate", to.Value));
                }

                var result = await _dbContext.Database.SqlQuery<T>(sql, parameters.ToArray()).FirstOrDefaultAsync();
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetRawDataAsync for {columnName}, VehicleId: {vehicleId}, From: {from}, To: {to}", ex);
                throw;
            }
        }

        private async Task<T> GetRawDataFromApiAsync<T>(string columnName, int? vehicleId, long companyId)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(30);
                    var dateString = DateTime.Today.ToString("yyyy-MM-dd");

                    var apiUrl = $"{_microserviceBaseUrl}/Trip/SingleVehicleTripData?companyId={companyId}&vehicleId={vehicleId}&date={dateString}";

                    var response = await client.GetAsync(apiUrl);
                    if (response.IsSuccessStatusCode)
                    {
                        var json = await response.Content.ReadAsStringAsync();
                        var apiResponse = JsonConvert.DeserializeObject<TripSummaryApiResponse>(json);

                        if (apiResponse.Vehicle != null && apiResponse.Vehicle.Any())
                        {
                            foreach (var vehicle in apiResponse.Vehicle)
                            {
                                foreach (var trip in vehicle.Trips)
                                {
                                    object columnValue = null;

                                    switch (columnName.ToLower())
                                    {
                                        case "vehicleid":
                                            columnValue = vehicle.VehicleId;
                                            break;
                                        case "tripdate":
                                            columnValue = trip.Date;
                                            break;
                                        case "topspeed":
                                            columnValue = trip.TripSummary.TopSpeed;
                                            break;
                                        case "avgspeed":
                                            columnValue = trip.TripSummary.AvgSpeed;
                                            break;
                                        case "traveldurationmin":
                                            columnValue = trip.TripSummary.TravelDurationMin;
                                            break;
                                        case "idledurationmin":
                                            columnValue = trip.TripSummary.IdleDurationMin;
                                            break;
                                        case "stopdurationmin":
                                            columnValue = trip.TripSummary.StopDurationMin;
                                            break;
                                        case "totaldurationmin":
                                            columnValue = trip.TripSummary.TotalDurationMin;
                                            break;
                                        case "fuelcons":
                                            columnValue = trip.TripSummary.FuelCons;
                                            break;
                                        case "fuelcost":
                                            columnValue = trip.TripSummary.FuelCost;
                                            break;
                                        case "idleviolationdurationmin":
                                            columnValue = trip.TripSummary.IdleViolationDurationMin;
                                            break;
                                        case "distancetraveled":
                                            columnValue = trip.TripSummary.DistanceTraveled;
                                            break;
                                        default:
                                            _logger.Warn($"Unknown column name requested from API: {columnName}");
                                            break;
                                    }

                                    if (columnValue != null)
                                    {
                                        return (T)Convert.ChangeType(columnValue, typeof(T));
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        _logger.Info($"Failed to fetch data from API for VehicleId: {vehicleId} Status: {response.StatusCode}");
                    }
                }

                return default;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetRawDataFromApiAsync for {columnName}, VehicleId: {vehicleId}", ex);
                return default;
            }
        }

        public async Task<T> GetAggregateDataAsync<T>(string aggregate, string columnName, DateTime? from, DateTime? to, int? vehicleId, long companyId)
        {
            try
            {
                T result = default;

                bool needsHistoricalData = from.HasValue && from.Value.Date < DateTime.Today;
                bool needsTodayData = !to.HasValue || to.Value.Date >= DateTime.Today;

                if (needsTodayData && needsHistoricalData)
                {
                    _logger.Info($"Processing date range that spans both historical data and today: {from} to {to}");

                    T historicalResult = default;
                    DateTime yesterday = DateTime.Today.AddDays(-1);

                    var sql = $"SELECT {aggregate}([{columnName}]) FROM AnalyticsTripSummary WITH(NOLOCK) WHERE CompanyId = @CompanyId";
                    var parameters = new List<SqlParameter>
                    {
                        new SqlParameter("@CompanyId", companyId)
                    };

                    if (vehicleId.HasValue)
                    {
                        sql += " AND VehicleId = @VehicleId";
                        parameters.Add(new SqlParameter("@VehicleId", vehicleId.Value));
                    }

                    sql += " AND TripDate >= @FromDate";
                    parameters.Add(new SqlParameter("@FromDate", from.Value));

                    sql += " AND TripDate <= @ToDate";
                    parameters.Add(new SqlParameter("@ToDate", yesterday));

                    historicalResult = await _dbContext.Database.SqlQuery<T>(sql, parameters.ToArray()).FirstOrDefaultAsync();
                    _logger.Info($"Historical data result for {from} to {yesterday}: {historicalResult}");

                    T todayResult = await GetAggregateDataFromApiAsync<T>(aggregate, columnName, vehicleId, companyId);
                    _logger.Info($"Today's data result: {todayResult}");

                    if (!EqualityComparer<T>.Default.Equals(historicalResult, default(T)) ||
                        !EqualityComparer<T>.Default.Equals(todayResult, default(T)))
                    {
                        result = CombineAggregateResults(aggregate, historicalResult, todayResult);
                        _logger.Info($"Combined result: {result}");
                        return result;
                    }
                }
                else if (needsTodayData && !needsHistoricalData)
                {
                    _logger.Info($"Processing today's data only");
                    result = await GetAggregateDataFromApiAsync<T>(aggregate, columnName, vehicleId, companyId);
                    return result;
                }
                else if (!needsTodayData && needsHistoricalData)
                {
                    _logger.Info($"Processing historical data only: {from} to {to}");
                    var sql = $"SELECT {aggregate}([{columnName}]) FROM AnalyticsTripSummary WITH(NOLOCK) WHERE CompanyId = @CompanyId";
                    var parameters = new List<SqlParameter>
                    {
                        new SqlParameter("@CompanyId", companyId)
                    };

                    if (vehicleId.HasValue)
                    {
                        sql += " AND VehicleId = @VehicleId";
                        parameters.Add(new SqlParameter("@VehicleId", vehicleId.Value));
                    }

                    if (from.HasValue)
                    {
                        sql += " AND TripDate >= @FromDate";
                        parameters.Add(new SqlParameter("@FromDate", from.Value));
                    }

                    if (to.HasValue)
                    {
                        sql += " AND TripDate <= @ToDate";
                        parameters.Add(new SqlParameter("@ToDate", to.Value));
                    }

                    result = await _dbContext.Database.SqlQuery<T>(sql, parameters.ToArray()).FirstOrDefaultAsync();
                    return result;
                }

                _logger.Warn($"Default fallback query for {aggregate}({columnName}), VehicleId: {vehicleId}, From: {from}, To: {to}");
                var fallbackSql = $"SELECT {aggregate}([{columnName}]) FROM AnalyticsTripSummary WITH(NOLOCK) WHERE CompanyId = @CompanyId";
                var fallbackParams = new List<SqlParameter>
                {
                    new SqlParameter("@CompanyId", companyId)
                };

                if (vehicleId.HasValue)
                {
                    fallbackSql += " AND VehicleId = @VehicleId";
                    fallbackParams.Add(new SqlParameter("@VehicleId", vehicleId.Value));
                }

                if (from.HasValue)
                {
                    fallbackSql += " AND TripDate >= @FromDate";
                    fallbackParams.Add(new SqlParameter("@FromDate", from.Value));
                }

                if (to.HasValue && to.Value.Date < DateTime.Today)
                {
                    fallbackSql += " AND TripDate <= @ToDate";
                    fallbackParams.Add(new SqlParameter("@ToDate", to.Value));
                }
                else if (to.HasValue)
                {
                    fallbackSql += " AND TripDate <= @ToDate";
                    fallbackParams.Add(new SqlParameter("@ToDate", DateTime.Today.AddDays(-1)));
                }

                result = await _dbContext.Database.SqlQuery<T>(fallbackSql, fallbackParams.ToArray()).FirstOrDefaultAsync();
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetAggregateDataAsync for {aggregate}({columnName}), VehicleId: {vehicleId}, From: {from}, To: {to}", ex);
                throw;
            }
        }

        private async Task<T> GetAggregateDataFromApiAsync<T>(string aggregate, string columnName, int? vehicleId, long companyId)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(30);
                    var dateString = DateTime.Today.ToString("yyyy-MM-dd");

                    var apiUrl = $"{_microserviceBaseUrl}/Trip/SingleVehicleTripData?companyId={companyId}&vehicleId={vehicleId}&date={dateString}";

                    var response = await client.GetAsync(apiUrl);
                    if (response.IsSuccessStatusCode)
                    {
                        var json = await response.Content.ReadAsStringAsync();
                        var apiResponse = JsonConvert.DeserializeObject<TripSummaryApiResponse>(json);

                        if (apiResponse.Vehicle != null && apiResponse.Vehicle.Any())
                        {
                            var values = new List<object>();

                            foreach (var vehicle in apiResponse.Vehicle)
                            {
                                foreach (var trip in vehicle.Trips)
                                {
                                    object columnValue = null;

                                    switch (columnName.ToLower())
                                    {
                                        case "vehicleid":
                                            columnValue = vehicle.VehicleId;
                                            break;
                                        case "tripdate":
                                            columnValue = trip.Date;
                                            break;
                                        case "topspeed":
                                            columnValue = trip.TripSummary.TopSpeed;
                                            break;
                                        case "avgspeed":
                                            columnValue = trip.TripSummary.AvgSpeed;
                                            break;
                                        case "traveldurationmin":
                                            columnValue = trip.TripSummary.TravelDurationMin;
                                            break;
                                        case "idledurationmin":
                                            columnValue = trip.TripSummary.IdleDurationMin;
                                            break;
                                        case "stopdurationmin":
                                            columnValue = trip.TripSummary.StopDurationMin;
                                            break;
                                        case "totaldurationmin":
                                            columnValue = trip.TripSummary.TotalDurationMin;
                                            break;
                                        case "fuelcons":
                                            columnValue = trip.TripSummary.FuelCons;
                                            break;
                                        case "fuelcost":
                                            columnValue = trip.TripSummary.FuelCost;
                                            break;
                                        case "idleviolationdurationmin":
                                            columnValue = trip.TripSummary.IdleViolationDurationMin;
                                            break;
                                        case "distancetraveled":
                                            columnValue = trip.TripSummary.DistanceTraveled;
                                            break;
                                        default:
                                            _logger.Warn($"Unknown column name requested from API: {columnName}");
                                            break;
                                    }

                                    if (columnValue != null)
                                    {
                                        values.Add(columnValue);
                                    }
                                }
                            }

                            if (values.Any())
                            {
                                object aggregateValue = null;

                                switch (aggregate.ToLower())
                                {
                                    case "sum":
                                        aggregateValue = CalculateSum(values);
                                        break;
                                    case "avg":
                                        aggregateValue = CalculateAverage(values);
                                        break;
                                    case "min":
                                        aggregateValue = CalculateMin(values);
                                        break;
                                    case "max":
                                        aggregateValue = CalculateMax(values);
                                        break;
                                    case "count":
                                        aggregateValue = values.Count;
                                        break;
                                    default:
                                        _logger.Warn($"Unsupported aggregate function: {aggregate}");
                                        break;
                                }

                                if (aggregateValue != null)
                                {
                                    return (T)Convert.ChangeType(aggregateValue, typeof(T));
                                }
                            }
                        }
                    }
                    else
                    {
                        _logger.Info($"Failed to fetch data from API for VehicleId: {vehicleId} Status: {response.StatusCode}");
                    }
                }

                return default;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetAggregateDataFromApiAsync for {aggregate}({columnName}), VehicleId: {vehicleId}", ex);
                return default;
            }
        }

        private object CalculateSum(List<object> values)
        {
            if (!values.Any())
                return 0;

            if (values.First() is int)
                return values.Sum(v => Convert.ToInt32(v));
            else if (values.First() is long)
                return values.Sum(v => Convert.ToInt64(v));
            else if (values.First() is double)
                return values.Sum(v => Convert.ToDouble(v));
            else if (values.First() is decimal)
                return values.Sum(v => Convert.ToDecimal(v));
            else
                return values.Sum(v => Convert.ToDouble(v));
        }

        private object CalculateAverage(List<object> values)
        {
            if (!values.Any())
                return 0;

            if (values.First() is int)
                return values.Average(v => Convert.ToInt32(v));
            else if (values.First() is long)
                return values.Average(v => Convert.ToInt64(v));
            else if (values.First() is double)
                return values.Average(v => Convert.ToDouble(v));
            else if (values.First() is decimal)
                return values.Average(v => Convert.ToDecimal(v));
            else
                return values.Average(v => Convert.ToDouble(v));
        }

        private object CalculateMin(List<object> values)
        {
            if (!values.Any())
                return null;

            if (values.First() is int)
                return values.Min(v => Convert.ToInt32(v));
            else if (values.First() is long)
                return values.Min(v => Convert.ToInt64(v));
            else if (values.First() is double)
                return values.Min(v => Convert.ToDouble(v));
            else if (values.First() is decimal)
                return values.Min(v => Convert.ToDecimal(v));
            else if (values.First() is DateTime)
                return values.Min(v => Convert.ToDateTime(v));
            else
                return values.Min(v => Convert.ToDouble(v));
        }

        private object CalculateMax(List<object> values)
        {
            if (!values.Any())
                return null;

            if (values.First() is int)
                return values.Max(v => Convert.ToInt32(v));
            else if (values.First() is long)
                return values.Max(v => Convert.ToInt64(v));
            else if (values.First() is double)
                return values.Max(v => Convert.ToDouble(v));
            else if (values.First() is decimal)
                return values.Max(v => Convert.ToDecimal(v));
            else if (values.First() is DateTime)
                return values.Max(v => Convert.ToDateTime(v));
            else
                return values.Max(v => Convert.ToDouble(v));
        }

        private T CombineAggregateResults<T>(string aggregate, T result1, T result2)
        {
            try
            {
                if (EqualityComparer<T>.Default.Equals(result1, default(T)))
                    return result2;
                if (EqualityComparer<T>.Default.Equals(result2, default(T)))
                    return result1;

                dynamic val1 = result1;
                dynamic val2 = result2;

                switch (aggregate.ToLower())
                {
                    case "sum":
                        return (T)(object)(val1 + val2);
                    case "avg":
                        return (T)(object)((val1 + val2) / 2);
                    case "min":
                        return (T)(object)(val1 < val2 ? val1 : val2);
                    case "max":
                        return (T)(object)(val1 > val2 ? val1 : val2);
                    case "count":
                        return (T)(object)(val1 + val2);
                    default:
                        _logger.Warn($"Unsupported aggregate function for combining: {aggregate}");
                        return !EqualityComparer<T>.Default.Equals(result1, default(T)) ? result1 : result2;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error combining aggregate results for {aggregate}: {ex.Message}", ex);
                return !EqualityComparer<T>.Default.Equals(result1, default(T)) ? result1 : result2;
            }
        }

        public async Task<Dictionary<string, object>> GetLookupDataAsync(List<LookupOperation> lookups, DateTime? from, DateTime? to, List<int> vehicleIds, long companyId)
        {
            var results = new Dictionary<string, object>();

            try
            {
                foreach (var lookup in lookups)
                {
                    string whereClause = "WHERE CompanyId = @CompanyId";
                    string joinClause = string.Empty;
                    var parameters = new List<SqlParameter>
                    {
                        new SqlParameter("@CompanyId", companyId)
                    };

                    if (vehicleIds != null && vehicleIds.Any())
                    {
                        for (int i = 0; i < vehicleIds.Count; i++)
                        {
                            var paramName = $"@VehicleId{i}";
                            parameters.Add(new SqlParameter(paramName, vehicleIds[i]));
                        }
                        whereClause += $" AND AnalyticsTripSummary.VehicleId IN ({string.Join(", ", vehicleIds.Select((_, i) => $"@VehicleId{i}"))})";
                    }

                    if (from.HasValue)
                    {
                        whereClause += " AND TripDate >= @FromDate";
                        parameters.Add(new SqlParameter("@FromDate", from.Value));
                    }

                    if (to.HasValue)
                    {
                        whereClause += " AND TripDate <= @ToDate";
                        parameters.Add(new SqlParameter("@ToDate", to.Value));
                    }

                    if (lookup.Filters != null)
                    {
                        int filterIndex = 0;
                        foreach (var filter in lookup.Filters)
                        {
                            var paramName = $"@Filter{filterIndex}_{filter.Column.Replace(" ", "")}";
                            whereClause += $" AND {filter.Column} {filter.Operator} {paramName}";
                            parameters.Add(new SqlParameter(paramName, filter.Value));
                            filterIndex++;
                        }
                    }

                    if (lookup.Joins != null && lookup.Joins.Any())
                    {
                        foreach (var join in lookup.Joins)
                        {
                            joinClause += $" {join.JoinType} JOIN {join.Table} ON {join.Condition}";
                        }
                    }

                    string targetColumn = lookup.TargetColumn;
                    string keyColumn = lookup.KeyColumn;
                    if (targetColumn.Contains("."))
                    {
                        targetColumn = targetColumn.Split('.').Last();
                    }
                    if (keyColumn != null && keyColumn.Contains("."))
                    {
                        keyColumn = keyColumn.Split('.').Last();
                    }

                    string selectClause = lookup.ReturnKeyColumn && !string.IsNullOrEmpty(keyColumn)
                        ? $"[{targetColumn}], [{keyColumn}]"
                        : $"[{targetColumn}]";

                    string sql = string.Empty;

                    switch (lookup.OperationType)
                    {
                        case LookupOperationType.Max:
                        case LookupOperationType.Min:
                            string orderDirection = lookup.OperationType == LookupOperationType.Max ? "DESC" : "ASC";
                            sql = $@"
                                SELECT TOP 1 {selectClause}
                                FROM AnalyticsTripSummary WITH(NOLOCK)
                                {joinClause}
                                {whereClause}
                                ORDER BY [{lookup.SourceColumn}] {orderDirection}, TripDate ASC";
                            break;

                        case LookupOperationType.First:
                        case LookupOperationType.Last:
                            string orderBy = lookup.OperationType == LookupOperationType.First ? "ASC" : "DESC";
                            sql = $@"
                                SELECT TOP 1 {selectClause}
                                FROM AnalyticsTripSummary WITH(NOLOCK)
                                {joinClause}
                                {whereClause}
                                ORDER BY TripDate {orderBy}";
                            break;

                        case LookupOperationType.Raw:
                        default:
                            sql = $@"
                                SELECT TOP 1 {selectClause}
                                FROM AnalyticsTripSummary WITH(NOLOCK)
                                {joinClause}
                                {whereClause}";
                            break;
                    }

                    var result = await ExecuteQueryAsync(sql, parameters);
                    if (result != null && result.Any())
                    {
                        var row = result.First();
                        results.Add($"{lookup.TargetColumn}-{lookup.OperationType}", TypeHelper.ConvertToType(row[targetColumn], lookup.TargetDataType));
                        if (lookup.ReturnKeyColumn && !string.IsNullOrEmpty(keyColumn))
                        {
                            results.Add($"{keyColumn}-{lookup.OperationType}", TypeHelper.ConvertToType(row[keyColumn], lookup.KeyDataType));
                        }
                    }
                    else
                    {
                        results.Add($"{lookup.TargetColumn}-{lookup.OperationType}", null);
                        if (lookup.ReturnKeyColumn && !string.IsNullOrEmpty(keyColumn))
                        {
                            results.Add($"{keyColumn}-{lookup.OperationType}", null);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Error in GetLookupDataAsync", ex);
                throw;
            }

            return results;
        }

        private async Task<List<Dictionary<string, object>>> ExecuteQueryAsync(string sql, List<SqlParameter> parameters)
        {
            using (var connection = new SqlConnection(((SqlConnection)_dbContext.Database.Connection).ConnectionString))
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddRange(parameters.ToArray());
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        var result = new List<Dictionary<string, object>>();
                        while (await reader.ReadAsync())
                        {
                            var row = new Dictionary<string, object>();
                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                row[reader.GetName(i)] = reader.GetValue(i);
                            }
                            result.Add(row);
                        }
                        return result;
                    }
                }
            }
        }
        public async Task<Dictionary<string, object>> GetLiveDataAsync(List<LookupOperation> lookups, int? vehicleId, long companyId)
        {
            VzoneTrackCommonBusiness _vtCommonBusiness = new VzoneTrackCommonBusiness();

            var query = from l in _dbContext.LastData
                        join v in _dbContext.Vehicle on l.VehicleID equals v.ID
                        join d in _dbContext.Driver on l.DriverID equals d.ID
                        join c in _dbContext.V32DriverIDCard on d.DriverCardID equals c.DriverCardID into cardJoin
                        from c in cardJoin.DefaultIfEmpty()
                        where l.VehicleID == vehicleId
                        select new
                        {
                            v.DeviceID,
                            v.DisplayText,
                            v.RegistrationNo,
                            v.Make,
                            v.Model,
                            l.Speed,
                            l.Latitude,
                            l.Longitude,
                            l.TrackTime,
                            d.DriverName,
                            d.MobileNo,
                            CardNumber = c != null ? c.CardNumber : null,
                            l.Status,
                            l.IsLandmark,
                            l.LandmarkName,
                            l.LocationName
                        };

            var dataRow = await query.FirstOrDefaultAsync();

            if (dataRow == null)
                return null;

            var ignitionStatus = _vtCommonBusiness.GetIgnitionString(
                _vtCommonBusiness.GetIgnition((int)dataRow.Status, (double)dataRow.Speed));

            var locationName = (bool)dataRow.IsLandmark ? dataRow.LandmarkName : dataRow.LocationName;

            var locationUrl = Utilities.GetGoogleMapLocationUrl(
                (double)dataRow.Latitude, (double)dataRow.Longitude);

            var result = new Dictionary<string, object>
            {
                { "DisplayText", dataRow.DisplayText },
                { "Model", dataRow.Make + " " + dataRow.Model },
                { "Speed", dataRow.Speed },
                { "Latitude", Convert.ToDecimal(dataRow.Latitude) },
                { "Longitude", Convert.ToDecimal(dataRow.Longitude) },
                { "TrackTime", Convert.ToDateTime(dataRow.TrackTime) },
                { "DriverName", dataRow.DriverName },
                { "ContactNo", dataRow.MobileNo },
                { "Key", dataRow.CardNumber },
                { "IgnitionStatus", ignitionStatus },
                { "LocationName", locationName },
                { "LocationUrl", locationUrl },
                { "LastReceived", _dbContext.Database.SqlQuery<string>(
                                    "SELECT dbo.udf_GetLastTime(@p0, @p1)", dataRow.TrackTime, dataRow.DeviceID
                                    ).FirstOrDefault() }
            };

            return result;
        }

        private async Task<object> GetScalarValueAsync(string sql, List<SqlParameter> parameters)
        {
            using (var command = _dbContext.Database.Connection.CreateCommand())
            {
                command.CommandText = sql;
                foreach (var param in parameters)
                    command.Parameters.Add(param);

                if (command.Connection.State != ConnectionState.Open)
                    await command.Connection.OpenAsync();

                return await command.ExecuteScalarAsync();
            }
        }

        public async Task<Dictionary<string, object>> GenerateVehicleRouteKmlAsync(DateTime? from, DateTime? to, int? vehicleId, long companyId, int userId)
        {
            try
            {
                TrackingMiddleware trackingMiddleware = new TrackingMiddleware();
                string kmlUrl = await trackingMiddleware.GenerateVehicleRouteKmlAsync(companyId, userId, (int)vehicleId, (DateTime)from);

                var response = new Dictionary<string, object>
                {
                    { "Status", "Success" },
                    { "KmlUrl", kmlUrl }
                };

                return response;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GenerateVehicleRouteKmlAsync. VehicleId: {vehicleId}, From: {from}, To: {to}", ex);              
                throw;
            }
        }
    }
}