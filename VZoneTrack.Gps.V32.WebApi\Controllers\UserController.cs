﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using VZoneTrack.Gps.V32.WebApi.Interfaces;
using VZoneTrack.Gps.V32.WebApi.Models;
using VZoneTrack.Gps.V32.Business;

namespace VZoneTrack.Gps.V32.WebApi.Controllers
{
    public class LoginController : ControllerBase
    {
        private readonly ITokenService _tokenService;

        public LoginController(ITokenService tokenService)
        {
            _tokenService = tokenService;
        }

        [Authorize]
        [HttpGet]
        [Route("Validate")]
        public IHttpActionResult ValidateToken()
        {
            string userName = GetUserDetails();
            string apiType = GetApiType();
            string requestType = GetRequestType();           
            if (userName != null)
            {
                return Ok(new
                {  
                    message = "AUTH_SUCCESS",
                    data = userName
                });
            }
            return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.Unauthorized, "Token is Invalid!"));
        }

        [HttpGet]
        [Route("Regenerate")]
        public IHttpActionResult RegenerateToken(string RefreshToken)
        {
            string NewAccessToken = _tokenService.CreateTokenFromRefreshToken(RefreshToken);
            if(NewAccessToken != null)
            {
                return Ok(new
                {
                    Token = NewAccessToken
                });
            }
            return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.Unauthorized, "Refresh Token is Invalid!"));
        }


        [Route("Authenticate/{Username}/{Password}/{IsInternal?}")]
        public IHttpActionResult GetToken(string username, string password, bool isinternal=false)
        {
            string saltValue = ConfigurationManager.AppSettings["PasswordSalt"].ToString();
            byte[] saltHash = Convert.FromBase64String(saltValue);
            HMACSHA512 DecryptedHmac = new HMACSHA512(saltHash);

            AppUser user = null;

            if(isinternal)
            {
                V41ApiToken v41ApiTokenModel = null;
                using (VZoneTrackEntities context = new VZoneTrackEntities())
                {
                    var query = from at in context.V41ApiToken
                                where at.Username.ToUpper() == username.ToUpper()
                                select at;

                    v41ApiTokenModel = query.FirstOrDefault<V41ApiToken>();
                }
                if (v41ApiTokenModel != null)
                {
                    user = new AppUser
                    {
                        UserName = username,
                        PasswordHash = Convert.FromBase64String(v41ApiTokenModel.PasswordHash),
                        PasswordSalt = saltHash,
                        IsPrivate = true,
                        RequestType = v41ApiTokenModel.RequestType
                    };
                }
            }
            else
            {
                User userModel = null;

                using (VZoneTrackEntities context = new VZoneTrackEntities())
                {
                    var query = from usr in context.Users
                                where usr.UserName.ToUpper() == username.ToUpper()
                                select usr;

                    userModel = query.FirstOrDefault<User>();
                }
                if(userModel!=null)
                {
                    string HashPassword = Convert.ToBase64String(DecryptedHmac.ComputeHash(Encoding.UTF8.GetBytes(userModel.Password)));
                    user = new AppUser
                    {
                        UserName = username,
                        PasswordHash = Convert.FromBase64String(HashPassword), //Convert.FromBase64String(   userModel.Password),
                        PasswordSalt = saltHash,
                        IsPrivate = false, 
                    };
                }
            }

            if(user == null)
            {
                return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.Unauthorized, "Please use a correct credential!"));
            }

            byte[] computedHash = DecryptedHmac.ComputeHash(Encoding.UTF8.GetBytes(password));
            for (int i = 0; i < computedHash.Length; i++)
            {
                if (computedHash[i] != user.PasswordHash[i]) return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.Unauthorized, "Please use a correct credential!"));
            }
            return Ok(new UserDto
            {
                Username = user.UserName,
                Token = _tokenService.CreateToken(user),
                RefreshToken = _tokenService.GenerateRefreshToken(user)
            });
        }
    }
}