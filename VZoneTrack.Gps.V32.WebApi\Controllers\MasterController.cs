﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using VZoneTrack.Gps.V32.WebApi.Code;
using VZoneTrack.Gps.V32.WebApi.Models;

namespace VZoneTrack.Gps.V32.WebApi.Controllers
{
    [Authorize]
    public class MasterController : ControllerBase
    {
        [HttpGet]
        [Route("Master/VehicleInfo/{AssetCode?}")]
        public IHttpActionResult GetVehicles([FromUri] string AssetCode= "") //DateTime Format {yyyyMMddHHmmss}
        {
            string userName = GetUserDetails();
            string apiType = GetApiType();
            string requestType = GetRequestType();
            bool _is2FAVerified = TwoFactorAuthenticateUser(userName, apiType);

            if (apiType == "external" && requestType == "other" && userName != null && _is2FAVerified)
            {
                long _companyId = EntityProvider.GetCompanyIdFromUsername(userName);
                List<VehicleInfo> vehicleInfoCollection = EntityProvider.GetVehicleInfo(_companyId, AssetCode);
                
                return Ok(new
                {
                    message = "SUCCESS",
                    data = vehicleInfoCollection
                });
            }
            return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.Unauthorized, "Unauthorized User!"));
        }

        [HttpPost]
        [Route("Master/AddVehicle")]
        public IHttpActionResult AddVehicle([FromBody] JobInfo jobInfo)
        {
            try
            {
                string userName = GetUserDetails();
                string apiType = GetApiType();
                string requestType = GetRequestType();
                bool _is2FAVerified = TwoFactorAuthenticateUser(userName, apiType);

                if (apiType == "internal" && requestType == "AppLink" && userName == "vzoneapps" && _is2FAVerified)
                {
                    if (jobInfo == null)
                    {
                        return BadRequest("Job Info cannot be empty");
                    }

                    int result = EntityProvider.AddVehicle(jobInfo);

                    if (result > 0)
                    {
                        return Ok(new { success = true, message = "Vehicle added successfully" });
                    }
                    else
                    {
                        return Ok(new { success = false, message = "Error while adding Vehicle" });
                    }
                }
                else
                    return Ok(new { success = false, message = "Unauthorized User" });
            }
            catch (Exception ex)
            {   
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        [Route("Master/AddDevice")]
        public IHttpActionResult AddDevice([FromBody] DeviceInfo deviceInfo)
        {
            try
            {
                string userName = GetUserDetails();
                string apiType = GetApiType();
                string requestType = GetRequestType();
                bool _is2FAVerified = TwoFactorAuthenticateUser(userName, apiType);

                if (apiType == "internal" && requestType == "AppLink" && userName == "vzoneapps" && _is2FAVerified)
                {
                    if (deviceInfo == null)
                    {
                        return BadRequest("Device Info cannot be empty");
                    }

                    int result = EntityProvider.AddDevice(deviceInfo);

                    if (result > 0)
                    {
                        return Ok(new { success = true, message = "Device added successfully" });
                    }
                    else
                    {
                        return Ok(new { success = false, message = "Error while adding Device" });
                    }
                }
                else
                    return Ok(new { success = false, message = "Unauthorized User" });
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }
    }
}