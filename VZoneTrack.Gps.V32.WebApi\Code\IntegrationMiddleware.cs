﻿using log4net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RabbitMQ.Client;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Web;
using VZoneTrack.Gps.V32.WebApi.Models;

namespace VZoneTrack.Gps.V32.WebApi.Code
{
    public class IntegrationMiddleware
    {
        private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        public int ProcessHHTLogToQueue(JToken RequestParams)
    {
            List<V41HHTLog_AlainFarms> lstHHTLogs = new List<V41HHTLog_AlainFarms>();

            try
        {
            string _json = RequestParams["HHT"].ToString();

            if (!string.IsNullOrEmpty(_json))
            {
                DataTable dtHHTLog = JsonConvert.DeserializeObject<DataTable>(_json);

                dynamic _rabbitmqParams = ConfigParams.GetRabbitMQConnParams();
                var factory = new ConnectionFactory() { HostName = _rabbitmqParams.Host, UserName= _rabbitmqParams.UserName, Password= _rabbitmqParams.Password, VirtualHost= _rabbitmqParams.VirtualHost };
                using (var connection = factory.CreateConnection())
                using (var channel = connection.CreateModel())
                {  
                    channel.QueueDeclare(queue: ConfigParams.RabbitMqQueue, durable: true, exclusive: false, autoDelete: false, arguments: null);

                        foreach (DataRow drw in dtHHTLog.Rows)
                        {
                            string _hhtCode = drw["HHTCode"].ToString();

                            if (!string.IsNullOrEmpty(_hhtCode))
                            {
                                V41HHTLog_AlainFarms _V41HHTLogAlainFarms = new V41HHTLog_AlainFarms
                                {
                                    RouteNo = drw.Table.Columns.Contains("RouteNo") ? drw["RouteNo"].ToString() : string.Empty,
                                    HHTCode = Convert.ToInt32(_hhtCode),
                                    TimeStamp = drw.Table.Columns.Contains("Timestamp") ? (string.IsNullOrEmpty(drw["Timestamp"].ToString()) ? (DateTime?)null : Convert.ToDateTime(drw["Timestamp"].ToString())) : null,
                                    Status = drw.Table.Columns.Contains("Status") ? (string.IsNullOrEmpty(drw["Status"].ToString()) ? (short?)null : Convert.ToInt16(drw["Status"].ToString())) : null,
                                    DepotCode = drw.Table.Columns.Contains("DepotCode") ? drw["DepotCode"].ToString() : string.Empty,
                                    SubArea = drw.Table.Columns.Contains("SubArea") ? drw["SubArea"].ToString() : string.Empty,
                                    Division = drw.Table.Columns.Contains("Division") ? drw["Division"].ToString() : string.Empty,
                                    Supervisor = drw.Table.Columns.Contains("Supervisor") ? drw["Supervisor"].ToString() : string.Empty,
                                    Salesman = drw.Table.Columns.Contains("Salesman") ? drw["Salesman"].ToString() : string.Empty,
                                    CreatedOn = DateTime.UtcNow
                                };
                                lstHHTLogs.Add(_V41HHTLogAlainFarms);
                            }
                        }

                        string jsonHHTLogs = JsonConvert.SerializeObject(lstHHTLogs);
                        var body = System.Text.Encoding.UTF8.GetBytes(jsonHHTLogs);

                        channel.BasicPublish(
                            exchange: "",
                            routingKey: ConfigParams.RabbitMqQueue,
                            basicProperties: null,
                            body: body);
                    }
            }
        }
        catch (Exception ex)
        {
            log.Error(ex);
        }

        return lstHHTLogs.Count;
    }

        public int ProcessHHTLogAlAinFarms(JToken RequestParams)
        {
            int _result = 0;

            List<V41HHTLog_AlainFarms> lstHHTLogAlainFarms = new List<V41HHTLog_AlainFarms>();
            try
            {
                string _json = RequestParams["HHT"].ToString();

                if (!string.IsNullOrEmpty(_json))
                {
                    DataTable dtHHTLog = JsonConvert.DeserializeObject<DataTable>(_json);

                    foreach (DataRow drw in dtHHTLog.Rows)
                    {
                        string _hhtCode = drw["HHTCode"].ToString();

                        if (!string.IsNullOrEmpty(_hhtCode))
                        {
                            V41HHTLog_AlainFarms _V41HHTLogAlainFarms = new V41HHTLog_AlainFarms();
                            _V41HHTLogAlainFarms.RouteNo = drw.Table.Columns.Contains("RouteNo") ? drw["RouteNo"].ToString() : string.Empty;
                            _V41HHTLogAlainFarms.HHTCode = Convert.ToInt32(_hhtCode);

                            string _timeStamp = drw.Table.Columns.Contains("Timestamp") ? drw["Timestamp"].ToString() : string.Empty;
                            if (!string.IsNullOrEmpty(_timeStamp))
                                _V41HHTLogAlainFarms.TimeStamp = Convert.ToDateTime(_timeStamp);
                            else
                                _V41HHTLogAlainFarms.TimeStamp = null;

                            string _status = drw.Table.Columns.Contains("Status") ? drw["Status"].ToString() : string.Empty;
                            if (!string.IsNullOrEmpty(_status))
                                _V41HHTLogAlainFarms.Status = Convert.ToInt16(_status);
                            else
                                _V41HHTLogAlainFarms.Status = null;

                            _V41HHTLogAlainFarms.DepotCode = drw.Table.Columns.Contains("DepotCode") ? drw["DepotCode"].ToString() : string.Empty;
                            _V41HHTLogAlainFarms.SubArea = drw.Table.Columns.Contains("SubArea") ? drw["SubArea"].ToString() : string.Empty;
                            _V41HHTLogAlainFarms.Division = drw.Table.Columns.Contains("Division") ? drw["Division"].ToString() : string.Empty;
                            _V41HHTLogAlainFarms.Supervisor = drw.Table.Columns.Contains("Supervisor") ? drw["Supervisor"].ToString() : string.Empty;
                            _V41HHTLogAlainFarms.Salesman = drw.Table.Columns.Contains("Salesman") ? drw["Salesman"].ToString() : string.Empty;
                            _V41HHTLogAlainFarms.CreatedOn = DateTime.UtcNow;

                            lstHHTLogAlainFarms.Add(_V41HHTLogAlainFarms);
                        }
                    }
                    _result = InsertHHTLogAlAinFarms(lstHHTLogAlainFarms);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
            }
            return _result;
        }

        private int InsertHHTLogAlAinFarms(List<V41HHTLog_AlainFarms> HHTLogCollection)
        {
            int _result = 0;
            try
            {
                using (var context = new VZoneTrackEntities())
                {
                    foreach (V41HHTLog_AlainFarms hhtLog in HHTLogCollection)
                    {
                        context.V41HHTLog_AlainFarms.Add(hhtLog);
                    }
                    _result = context.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            return _result;            
        }
    }
}