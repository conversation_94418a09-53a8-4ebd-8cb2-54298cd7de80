﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using VZoneTrack.Gps.V32.Business;

namespace VZoneTrack.Gps.V32.WebApi.Models
{
    public class ReportCompany
    {

        public string CompanyName;
        public string userName;
        public string PhoneNo;
        public string Email;
        public string URL;
        public int TimeFormat;
        public string CompanyLogo;
        public int TimeZone;
        public int CompanyId;
        public int UserId;
        public DataTable CompanyDataSet;
        public void SetCompanyDetails(int Companyid, int Userid)
        {
            ReportBusiness report = new ReportBusiness();
            DataSet objCompanyDS = report.GetCompanyInfo(Companyid, Userid);
            CompanyName = objCompanyDS.Tables[0].Rows[0]["CompanyName"].ToString();
            userName = objCompanyDS.Tables[0].Rows[0]["UserName"].ToString();
            PhoneNo = objCompanyDS.Tables[0].Rows[0]["PhoneNo"].ToString();
            Email = objCompanyDS.Tables[0].Rows[0]["Email"].ToString();
            URL = objCompanyDS.Tables[0].Rows[0]["Url"].ToString();
            TimeFormat = Convert.ToInt32(objCompanyDS.Tables[0].Rows[0]["Timeformat"]);
            CompanyLogo = objCompanyDS.Tables[0].Rows[0]["Logo"].ToString();
            TimeZone = Convert.ToInt32(objCompanyDS.Tables[0].Rows[0]["TimeZone"]);
            CompanyId = Companyid;
            UserId = Userid;
            CompanyDataSet = objCompanyDS.Tables[0].Copy();
        }
    }

}