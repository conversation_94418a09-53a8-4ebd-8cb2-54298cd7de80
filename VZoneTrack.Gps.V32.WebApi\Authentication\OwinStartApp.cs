﻿using Microsoft.Owin.Security;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Owin;
using Microsoft.Owin.Security.Jwt;
using Microsoft.Owin.Host.SystemWeb;
using System.Net.Http;
using System.Threading.Tasks;
using System.Threading;
using System.Net;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Configuration;
using System.Security.Claims;
using System.Web.Http;

namespace VZoneTrack.Gps.V32.WebApi
{
    internal class TokenValidationHandler : DelegatingHandler
    {
        private static bool TryRetrieveToken(HttpRequestMessage request, out string token)
        {
            token = null;
            IEnumerable<string> authzHeaders;
            if (!request.Headers.TryGetValues("Authorization", out authzHeaders) || authzHeaders.Count() > 1)
            {
                return false;
            }
            var bearerToken = authzHeaders.ElementAt(0);
            token = bearerToken.StartsWith("Bearer ") ? bearerToken.Substring(7) : bearerToken;
            return true;
        }

        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            HttpStatusCode statusCode;
            string token;
            //determine whether a jwt exists or not
            if (!TryRetrieveToken(request, out token))
            {
                statusCode = HttpStatusCode.Unauthorized;
                //allow requests with no token - whether a action method needs an authentication can be set with the claimsauthorization attribute
                return base.SendAsync(request, cancellationToken);
            }

            try
            {
                DateTime now = DateTime.UtcNow;
                SymmetricSecurityKey securityKey = new SymmetricSecurityKey(Convert.FromBase64String(ConfigurationManager.AppSettings["PasswordSalt"].ToString()));

                SecurityToken securityToken;
                JwtSecurityTokenHandler handler = new JwtSecurityTokenHandler();
                TokenValidationParameters validationParameters = new TokenValidationParameters()
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = securityKey,
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ClockSkew = TimeSpan.Zero
                };
                //extract and assign the user of the jwt
                try
                {
                    Thread.CurrentPrincipal = handler.ValidateToken(token, validationParameters, out securityToken);
                    HttpContext.Current.User = handler.ValidateToken(token, validationParameters, out securityToken);

                    ClaimsIdentity identity = HttpContext.Current.User.Identity as ClaimsIdentity;
                    if (identity != null)
                    {
                        IEnumerable<Claim> claims = identity.Claims;
                        if(claims.Where(c => c.Type == "TokenType").First().Value == "Access")
                        {
                            return base.SendAsync(request, cancellationToken);
                        }
                    }
                    statusCode = HttpStatusCode.Unauthorized;
                }
                catch (ArgumentException ex)
                {
                    statusCode = HttpStatusCode.Unauthorized;
                }
            }
            catch (SecurityTokenValidationException e)
            {
                statusCode = HttpStatusCode.Unauthorized;
            }
            catch (Exception ex)
            {
                statusCode = HttpStatusCode.InternalServerError;                
            }

            //@josbol mentioned this issue in 2018

            //Since this solution pushes the request further down the pipeline, instead of immediately returning, this 
            //solution consumes more resources for each request, so this might not be the right solution for everybody.
            //Alternatively one could manually add the missing headers before returning,
            //but I don't think that would be a good idea.

            //- this line would return correct CORS header.- credit to 
            //return base.SendAsync(request, cancellationToken);

            return Task<HttpResponseMessage>.Factory.StartNew(() => new HttpResponseMessage(statusCode) { });            
        }
    }
}