﻿<?xml version="1.0"?>
<log4net>
  <appender name="errorAppender" type="log4net.Appender.RollingFileAppender">
    <filter type="log4net.Filter.LevelMatchFilter">
      <levelToMatch value="ERROR" />
    </filter>
    <filter type="log4net.Filter.DenyAllFilter" />
    <File value="Logs\error.log" />
    <PreserveLogFileNameExtension value="true" />
    <appendToFile value="true" />
    <rollingStyle value="Date" />
    <datePattern value="ddMMyyyy" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
    </layout>
  </appender>
  <appender name="infoAppender" type="log4net.Appender.RollingFileAppender">
    <filter type="log4net.Filter.LevelMatchFilter">
      <levelToMatch value="INFO" />
    </filter>
    <filter type="log4net.Filter.DenyAllFilter" />
    <File value="Logs\info.log" />
    <PreserveLogFileNameExtension value="true" />
    <appendToFile value="true" />
    <rollingStyle value="Date" />
    <datePattern value="ddMMyyyy" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
    </layout>
  </appender>
  <appender name="debugAppender" type="log4net.Appender.RollingFileAppender">
    <filter type="log4net.Filter.LevelMatchFilter">
      <levelToMatch value="DEBUG" />
    </filter>
    <filter type="log4net.Filter.DenyAllFilter" />
    <File value="Logs\debug.log" />
    <PreserveLogFileNameExtension value="true" />
    <appendToFile value="true" />
    <rollingStyle value="Date" />
    <datePattern value="ddMMyyyy" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
    </layout>
  </appender>
  <appender name="perfAppender" type="log4net.Appender.RollingFileAppender">
    <filter type="log4net.Filter.LevelMatchFilter">
      <levelToMatch value="INFO" />
    </filter>
    <filter type="log4net.Filter.DenyAllFilter" />
    <File value="Logs\perf.log" />
    <PreserveLogFileNameExtension value="true" />
    <appendToFile value="true" />
    <rollingStyle value="Date" />
    <datePattern value="ddMMyyyy" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %logger - %message%newline" />
    </layout>
  </appender>
  <root>
    <level value="ALL" />
    <appender-ref ref="errorAppender" />
    <appender-ref ref="infoAppender" />
    <appender-ref ref="debugAppender" />
  </root>
  <logger name="Performance" additivity="false">
    <level value="ALL" />
    <appender-ref ref="perfAppender" />
  </logger>
</log4net>