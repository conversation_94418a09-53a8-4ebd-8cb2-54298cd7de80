﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Dynamic;
using System.Linq;
using System.Web;

namespace VZoneTrack.Gps.V32.WebApi.Models
{
    public class Integration
    {       
    }

    public static class ConfigParams
    {
        public static string RabbitMqQueue => ConfigurationManager.AppSettings["RABBITMQ_QUEUE"].ToString();
        public static dynamic GetRabbitMQConnParams()
        {
            string _strRabbitMQConn = ConfigurationManager.AppSettings["RABBITMQ_CONN_PARAMS"].ToString();
            string[] _strRabbitMQParams = _strRabbitMQConn.Split(',');

            dynamic _rabbitMQ = new ExpandoObject();
            _rabbitMQ.Host = _strRabbitMQParams[0].ToString().Trim();
            _rabbitMQ.UserName = _strRabbitMQParams[1].ToString().Trim();
            _rabbitMQ.Password = _strRabbitMQParams[2].ToString().Trim();
            _rabbitMQ.VirtualHost = _strRabbitMQParams[3].ToString().Trim();

            return _rabbitMQ;
        }
    }
}