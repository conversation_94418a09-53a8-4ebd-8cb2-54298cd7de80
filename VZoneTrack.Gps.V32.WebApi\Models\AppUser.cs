﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace VZoneTrack.Gps.V32.WebApi.Models
{
    public class UserDto
    {
        public string Username { get; set; }
        public string Token { get; set; }

        public string RefreshToken { get; set; }
    }

    public class AppUser
    {
        public int Id { get; set; }        
        public string UserName { get; set; }
        public byte[] PasswordHash { get; set; }
        public byte[] PasswordSalt { get; set; }
        public bool IsPrivate { get; set; }
        public string RequestType { get; set; }
    }


}