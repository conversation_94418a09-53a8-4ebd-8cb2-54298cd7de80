﻿using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Web;
using VZoneTrack.Gps.V32.WebApi.Interfaces;
using VZoneTrack.Gps.V32.WebApi.Models;
using VZoneTrack.Gps.V32.WebApi.Controllers;

namespace VZoneTrack.Gps.V32.WebApi.Services
{
    public class TokenService : ITokenService
    {
        private readonly SymmetricSecurityKey _key;

        public TokenService()
        {
            _key = new SymmetricSecurityKey(Convert.FromBase64String(ConfigurationManager.AppSettings["PasswordSalt"].ToString()));
        }

        public string CreateToken(AppUser user)
        {
            List<Claim> claims = new List<Claim>
           {
               new Claim(JwtRegisteredClaimNames.NameId,user.UserName)
           };

            Dictionary<string, object> OtherClaims = new Dictionary<string, object>();
            OtherClaims.Add(JwtRegisteredClaimNames.NameId, user.UserName);
            if (user.IsPrivate)
            {
                OtherClaims.Add("ApiType", "internal");
                OtherClaims.Add("RequestType", user.RequestType);                
            }
            else
            {
                OtherClaims.Add("ApiType", "external");
                OtherClaims.Add("RequestType", "other");
            }

            OtherClaims.Add("TokenType", "Access");

            SigningCredentials creds = new SigningCredentials(_key, SecurityAlgorithms.HmacSha512Signature);
            SecurityTokenDescriptor tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.Now.AddDays(Convert.ToInt32(ConfigurationManager.AppSettings["JWTKeyExpiry"].ToString())),
                SigningCredentials = creds,
                Claims = OtherClaims
            };
            JwtSecurityTokenHandler tokenHandler = new JwtSecurityTokenHandler();
            SecurityToken token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }


        public string GetUserDetails(ClaimsPrincipal User)
        {
            ClaimsIdentity identity = User.Identity as ClaimsIdentity;
            if (identity != null)
            {
                IEnumerable<Claim> claims = identity.Claims;
                return claims.ElementAt(0).Value;
            }
            return null;
        }

        public string GetRequestType(ClaimsPrincipal User)
        {
            ClaimsIdentity identity = User.Identity as ClaimsIdentity;
            if (identity != null)
            {
                IEnumerable<Claim> claims = identity.Claims;
                return claims.Where(c => c.Type == "RequestType").First().Value;
            }
            return null;
        }

        public string GetApiType(ClaimsPrincipal User)
        {
            ClaimsIdentity identity = User.Identity as ClaimsIdentity;
            if (identity != null)
            {
                IEnumerable<Claim> claims = identity.Claims;
                return claims.Where(c => c.Type == "ApiType").First().Value;
            }
            return null;
        }

        public string GetTokenType(ClaimsPrincipal User)
        {
            ClaimsIdentity identity = User.Identity as ClaimsIdentity;
            if (identity != null)
            {
                IEnumerable<Claim> claims = identity.Claims;
                return claims.Where(c => c.Type == "TokenType").First().Value;
            }
            return null;
        }

        public string CreateTokenFromRefreshToken(string RefreshToken)
        {
            try
            {
                SecurityToken securityToken;
                JwtSecurityTokenHandler handler = new JwtSecurityTokenHandler();
                TokenValidationParameters validationParameters = new TokenValidationParameters()
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = _key,
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ClockSkew = TimeSpan.Zero
                };

                ControllerBase _cb = new ControllerBase();

                ClaimsPrincipal User = handler.ValidateToken(RefreshToken, validationParameters, out securityToken);

                string UserName = GetUserDetails(User);

                if (GetTokenType(User) == "Refresh")
                {
                    List<Claim> claims = new List<Claim>
                    {
                        new Claim(JwtRegisteredClaimNames.NameId, UserName)
                    };

                    Dictionary<string, object> OtherClaims = new Dictionary<string, object>();
                    OtherClaims.Add(JwtRegisteredClaimNames.NameId, UserName);

                    OtherClaims.Add("ApiType", GetApiType(User));
                    OtherClaims.Add("RequestType", GetRequestType(User));
                    OtherClaims.Add("TokenType", "Access");

                    SigningCredentials creds = new SigningCredentials(_key, SecurityAlgorithms.HmacSha512Signature);
                    SecurityTokenDescriptor tokenDescriptor = new SecurityTokenDescriptor
                    {
                        Subject = new ClaimsIdentity(claims),
                        Expires = DateTime.Now.AddDays(Convert.ToInt32(ConfigurationManager.AppSettings["JWTKeyExpiry"].ToString())),
                        SigningCredentials = creds,
                        Claims = OtherClaims
                    };
                    JwtSecurityTokenHandler tokenHandler = new JwtSecurityTokenHandler();
                    SecurityToken token = tokenHandler.CreateToken(tokenDescriptor);
                    return tokenHandler.WriteToken(token);
                }
                return null;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public string GenerateRefreshToken(AppUser user)
        {
            List<Claim> claims = new List<Claim>
           {
               new Claim(JwtRegisteredClaimNames.NameId,user.UserName)
           };

            Dictionary<string, object> OtherClaims = new Dictionary<string, object>();
            OtherClaims.Add(JwtRegisteredClaimNames.NameId, user.UserName);

            if (user.IsPrivate)
            {
                OtherClaims.Add("ApiType", "internal");
                OtherClaims.Add("RequestType", user.RequestType);
            }
            else
            {
                OtherClaims.Add("ApiType", "external");
                OtherClaims.Add("RequestType", "other");
            }
            OtherClaims.Add("TokenType", "Refresh");            

            SigningCredentials creds = new SigningCredentials(_key, SecurityAlgorithms.HmacSha512Signature);
            SecurityTokenDescriptor tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.Now.AddDays(Convert.ToInt32(ConfigurationManager.AppSettings["JWTRefreshKeyExpiry"].ToString())),
                SigningCredentials = creds,
                Claims = OtherClaims
            };
            JwtSecurityTokenHandler tokenHandler = new JwtSecurityTokenHandler();
            SecurityToken token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }
       
    }
}