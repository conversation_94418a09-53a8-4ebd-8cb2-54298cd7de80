﻿using log4net;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Web;
using VZoneTrack.Gps.V32.Business;
using VZoneTrack.Gps.V32.WebApi.Models;

namespace VZoneTrack.Gps.V32.WebApi.Code
{
    public class ReportProcessingMiddleware
    {
        VzoneTrackReportBusiness report;
        ReportCompany Company;
        private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        private DataSet _masterData;
        private DataSet dsOut;
        private int ReportId;
        public ReportProcessingMiddleware(int reportId)
        {
            report = new VzoneTrackReportBusiness();
            Company = new ReportCompany();
            _masterData = null;
            dsOut = null;
            ReportId = reportId;
        }
        public DataSet processReport(JToken RequestParams)
        {
            Company.SetCompanyDetails(Convert.ToInt32(RequestParams["CompanyId"]), Convert.ToInt32(RequestParams["UserId"]));
  
            ReportBusiness rptData = new ReportBusiness();
            DataTable ReportDataSet = rptData.GetReportList(Convert.ToInt32(RequestParams["CompanyId"]), Convert.ToInt32(RequestParams["UserId"])).Tables[1];

            DataRow ReportRow = ReportDataSet.AsEnumerable().Where(r => r.Field<int>("ReportId") == ReportId).First();
            DataTable Rporttbl = ReportDataSet.Clone();
            Rporttbl.TableName = "ReportTable";
            Rporttbl.Rows.Add(ReportRow.ItemArray);

            (_masterData, dsOut) = report.GetReportDataSet(ReportId, Company.CompanyId);

            DateTime DateFrom = RequestParams["StartDate"] == null ? DateTime.UtcNow.Date : Convert.ToDateTime(RequestParams["StartDate"]);
            DateTime DateTo = RequestParams["EndDate"] == null ? DateTime.UtcNow.Date : Convert.ToDateTime(RequestParams["EndDate"]);
            int VehicleID = RequestParams["VehicleId"] == null ? 0 : Convert.ToInt32(RequestParams["VehicleId"]);
            string strVehicle = RequestParams["VehicleIdList"] == null ? "" : RequestParams["VehicleIdList"].ToString();

            try
            {
                if (ReportId == 36)
                    GenerateGCSReport(DateFrom, DateTo, VehicleID);
                else
                {

                    switch (ReportId)
                    {
                        case 1:
                            GenerateIdleReport(DateFrom, DateTo, strVehicle, 0);
                            break;
                        case 34:
                            GenerateIdleReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["IdleValue"]));
                            break;
                        case 17:
                            GenerateIdleReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["IdleValue"]));
                            break;
                        case 2:
                            GenerateTravelReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["TravelValue"]));
                            break;
                        case 16:
                            GenerateTravelReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["TravelValue"]));
                            break;
                        case 3:
                            GenerateStopReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["StopValue"]));
                            break;
                        case 4:
                            GenerateStartStopReport(DateFrom, DateTo, strVehicle, RequestParams["weekdays"].ToString());
                            break;
                        case 5:
                            GenerateJobSummaryReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 6:
                            GenerateTrackingReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 7:
                            GenerateIdleSummaryReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["IdleValue"]));
                            break;
                        case 8:
                            if (RequestParams["weekdays"].ToString() != "")
                                GenerateOfftimeTravelReport(DateFrom, DateTo, strVehicle, RequestParams["weekdays"].ToString());
                            break;
                        case 9:
                            GenerateOverSpeedReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["OverSpeedValue"]));
                            break;
                        case 10:
                            GenerateTripReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 11:
                            GenerateDailyKMReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 12:
                            GenerateSalikReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["SalikTime"]));
                            break;
                        case 13:
                            GenerateDataLogReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 14:
                            GenerateHarshEventReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 15:
                            GenerateWeekDaySummaryReport(DateFrom, DateTo, strVehicle, RequestParams["weekdays"].ToString());
                            break;
                        case 18:
                            GenerateWeekDaySummaryReport(DateFrom, DateTo, strVehicle, RequestParams["weekdays"].ToString());
                            break;
                        case 19:
                            GenerateDriverbasedTripSummaryReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 20:
                            GenerateLandmarkVisitedReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 21:
                            GenerateSensorLiveReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 22:
                            GenerateSensorHistoryReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 23:
                            GenerateRFIDTempReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 24:
                            GenerateRFIDTempTagReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 25:
                            GenerateTempEventLogReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 26:
                            GenerateTempLimitReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["MinTemp"]), Convert.ToInt32(RequestParams["MaxTemp"]));
                            break;
                        case 27:
                            GenerateRFIDbasedTemperatureGraph(DateFrom, DateTo, strVehicle, 1);
                            break;
                        case 28:
                            GenerateRFIDbasedTemperatureTagGraph(DateFrom, DateTo, strVehicle, 2);
                            break;
                        case 29:
                            GenerateAttendanceReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 35:
                            GenerateVehicleVisitedLandmarkReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["LandmarkId"]));
                            break;
                        case 37:
                            GenerateLandmarkBasedTripReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 38:
                            GenerateServiceScheduleReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 39:
                            GenerateexpenseLogReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 40:
                            GenerateOfftimeKMExcessIdleReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["IdleValue"]));
                            break;
                        case 41:
                            GenerateDriverJobDurationReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 42:
                            GenerateTempTagReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 43:
                            GenerateSOSReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 44:
                            GenerateAlertReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["AlertTypeId"]), RequestParams["AlertType"].ToString());
                            break;
                        case 45:
                            GenerateAcitvitySummaryReport(DateFrom, DateTo, strVehicle, Convert.ToDateTime(RequestParams["StartTime"]), Convert.ToDateTime(RequestParams["EndTime"]), Convert.ToInt32(RequestParams["OverSpeedValue"]), Convert.ToInt32(RequestParams["IdleValue"]), Convert.ToInt32(RequestParams["SalikTime"]), RequestParams["GroupList"].ToString());
                            break;
                        case 46:
                            GenerateFuelFillUpReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 47:
                            GenerateFuelConsumptionReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 48:
                            GenerateFuelTimeGraphReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 49:
                            GenerateFuelOdometerGraphReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 50:
                            GenerateFuelDistanceGraphReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 51:
                            GenerateEmployeePunchDetailReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 52:
                            GenerateTemperatureLevelReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 53:
                            GenerateIdleReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["IdleValue"]));
                            break;
                        case 54:
                            GenerateActivityDetailReport(DateFrom, DateTo, strVehicle);
                            break;
                        //case 55:
                        //    GenerateAlAinIdleReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["IdleValue"]), RequestParams["GroupList"].ToString());
                        //    break;
                        case 56:
                            GenerateAlAinOverSpeedReport(DateFrom, DateTo, strVehicle, RequestParams["GroupList"].ToString());
                            break;
                        case 31:
                            GenerateEmployeeActivityReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 32:
                            GenerateInOutPunchReport(DateFrom, DateTo);
                            break;
                        case 33:
                            GenerateUserLoginReport(DateFrom, DateTo);
                            break;
                        case 30:
                            GenerateDetailedPunchLogReport(DateFrom, DateTo);
                            break;
                        case 57:
                            GenerateTrackingSummaryReport(DateFrom, DateTo, strVehicle);
                            break;
                        case 58:
                            GenerateIdleViolationReport(DateFrom, DateTo, strVehicle, Convert.ToInt32(RequestParams["IdleAllowedValue"]));
                            break;

                    }
                    dsOut.Tables.Add(Rporttbl); 
                    dsOut.Tables.Add(Company.CompanyDataSet);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
            return dsOut;
        }
        #region "Activity Detail"
        private void GenerateActivityDetailReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtFuelPriceList = _masterData.Tables["FuelPrice"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    dsOut = report.GetAlAinActivityDetailReport(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, dsOut,"",true,false);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "Al Ain Idle"
        //private void GenerateAlAinIdleReport(DateTime StartDate, DateTime EndDate, string vehicleID, int idleDuration, string SelectedGroupName)
        //{
        //    try
        //    {
        //        DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
        //        DataTable dtVehicleDoorList = _masterData.Tables["DoorMaster"];
        //        DataTable dtFuelPriceList = _masterData.Tables["FuelPrice"];
        //        DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
        //        double _dieselPrice = 0;
        //        foreach (DataRow vehicleRow in _drFilteredVehicleList)
        //        {
        //            long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
        //            dsOut = report.GetAlAinIdleReport(vehicleRow, vehicleId, dtFuelPriceList, dtVehicleDoorList, StartDate, EndDate, dsOut, Company.CompanyId, idleDuration, out _dieselPrice,0,20,"",true,true,);
        //        }
        //        dsOut = report.GetAlAinReportDepoIdleSummary(dsOut, Company.CompanyId, SelectedGroupName, StartDate, EndDate);
        //        //DataRow[] slectedRows = dtFuelPriceList.Select("FuelTypeId = 2 AND IsActive = 1");
        //        //double FuelPrice = Convert.ToDouble(slectedRows[0]["Price"]);
        //        //string FuelPriceValue = Math.Round(FuelPrice, 2).ToString();
        //        string FuelPriceValue = Math.Round(_dieselPrice, 2).ToString();
        //        DataRow FuelPriceRow = dsOut.Tables[4].NewRow();
        //        FuelPriceRow["PropertyName"] = "FuelPrice";
        //        FuelPriceRow["Value"] = FuelPriceValue;
        //        dsOut.Tables[4].Rows.Add(FuelPriceRow);
        //    }
        //    catch (Exception ex)
        //    {
        //        log.Error(ex); dsOut = null;
        //    }
        //    return;
        //}
        #endregion
        #region "Al Ain Overspeed"
        private void GenerateAlAinOverSpeedReport(DateTime StartDate, DateTime EndDate, string vehicleID, string SelectedGroupName)
        {
            try
            {
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    dsOut = report.GetAlAinOverSpeedReport(vehicleRow, vehicleId, StartDate, EndDate, dsOut, Company.CompanyId,0);
                }
                dsOut = report.GetAlAinReportDepoOverSpeedSummary(dsOut, Company.CompanyId, SelectedGroupName, StartDate, EndDate);
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
            return;
        }
        #endregion
        #region "Idle Report"
        private void GenerateIdleReport(DateTime StartDate, DateTime EndDate, string vehicleID, int idleDuration)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtFuelPriceList = _masterData.Tables["FuelPrice"];
                DataRow[] _drFilteredVehicleList; 
                if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetIdleReport(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), idleDuration, Company.TimeFormat, ResuleTable, dtFuelPriceList, "{0}D:{1}H:{2}M:{3}S",true,false);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
            return;
        }
        #endregion
        #region Travel Report
        private void GenerateTravelReport(DateTime StartDate, DateTime EndDate, string vehicleID, int TravelDuration)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetTravelReport(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), TravelDuration, Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
            return;
        }
        #endregion
        #region "Stop Report"
        private void GenerateStopReport(DateTime StartDate, DateTime EndDate, string vehicleID, int stopDuration)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetStopReport(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), stopDuration, Company.TimeFormat, ResuleTable, "{0}D:{1}H:{2}M:{3}S",true,false);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "Start Stop Report"
        private void GenerateStartStopReport(DateTime StartDate, DateTime EndDate, string vehicleID, string Weekdays)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetStartStopDaywise(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Weekdays, Company.TimeFormat, ResuleTable, "{0}D:{1}H:{2}M:{3}S",false,true);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Job Summary Report
        private void GenerateJobSummaryReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResultTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtFuelPriceList = _masterData.Tables["FuelPrice"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResultTable = report.GetJobSummaryReport(vehicleRow, vehicleId, dtFuelPriceList, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResultTable, "{0}D:{1}H:{2}M:{3}S", true,false);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Tracking Report
        private void GenerateTrackingReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetTrackingReport(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "Idle Summary Report"
        private void GenerateIdleSummaryReport(DateTime StartDate, DateTime EndDate, string vehicleID, int IdleDuration)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetIdleSummaryReport(vehicleRow, vehicleId, StartDate, EndDate, Company.TimeFormat, IdleDuration, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "Offtime Travel Report"
        private void GenerateOfftimeTravelReport(DateTime StartDate, DateTime EndDate, string vehicleID, string Weekdays)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetOffTimeTravelReport(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), 0, 1, ResuleTable, Weekdays, "{0}D:{1}H:{2}M:{3}S",true,false);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Offtime KM & Idle Report
        private void GenerateOfftimeKMExcessIdleReport(DateTime StartDate, DateTime EndDate, string vehicleID, int IdleDuration)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetOffTimeSummary(vehicleRow, vehicleId, StartDate, EndDate, Convert.ToInt32(Company.CompanyId), IdleDuration, Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "Overspeed Report"
        private void GenerateOverSpeedReport(DateTime StartDate, DateTime EndDate, string vehicleID, int OverSpeedValue)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetOverspeedReport(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), OverSpeedValue, Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "Trip Report"
        private void GenerateTripReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtFuelPriceList = _masterData.Tables["FuelPrice"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetTripReport(vehicleRow, vehicleId, dtFuelPriceList, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "Landmark Trip Report"
        private void GenerateLandmarkBasedTripReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtFuelPriceList = _masterData.Tables["FuelPrice"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetLandmarkBasedTripReport(vehicleRow, vehicleId, dtFuelPriceList, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Daily KM Report
        private void GenerateDailyKMReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                EndDate = EndDate.AddSeconds(59);
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataRow[] _drFilteredVehicleList;
                if (!string.IsNullOrEmpty(vehicleID))
                    _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")");
                else
                    _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetDailyKmReport(vehicleRow, vehicleId, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "Salik Report"
        private void GenerateSalikReport(DateTime StartDate, DateTime EndDate, string vehicleID, int filterMinute)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetSalik(vehicleRow, vehicleId, StartDate, EndDate, Convert.ToInt32(Company.CompanyId), filterMinute, Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region SOS / Delivery Report
        private void GenerateSOSReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = null;
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                dsOut = new DataSet();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetSOSReport(vehicleRow, vehicleId, StartDate, EndDate, ResuleTable);
                }
                dsOut.Tables.Add(ResuleTable);
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Alert Report
        private void GenerateAlertReport(DateTime StartDate, DateTime EndDate, string vehicleID, int alertTypeId, string alertType)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetAlertReport(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, ResuleTable, alertTypeId);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Generate Activity Summary Report
        private void GenerateAcitvitySummaryReport(DateTime StartDate, DateTime EndDate, string vehicleID, DateTime? startDateTime, DateTime? endDateTime, int OverSpeedThreshold, int IdleDuration, int filterMinutes, string GroupIds)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                DateTime? offTimeStart = startDateTime;
                DateTime? offTimeEnd = endDateTime;
                DateTime tempTime;
                int overSpeedThreshold = Convert.ToInt32(OverSpeedThreshold);
                int ExcessIdleDuration = Convert.ToInt32(IdleDuration);
                int salikMinTimeThreshold = Convert.ToInt32(filterMinutes);
                string StrGroupNames = GroupIds;
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetActivitySummaryReport(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, ResuleTable, overSpeedThreshold, ExcessIdleDuration, salikMinTimeThreshold, offTimeStart, offTimeEnd, StrGroupNames);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Generate Fuel Fill-up Report
        private void GenerateFuelFillUpReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtFuelPriceList = _masterData.Tables["FuelPrice"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    bool isCanbusEnabled = Convert.ToBoolean(vehicleRow["IsCanBus"]);
                    if (isCanbusEnabled)
                    {
                        ResuleTable = report.GetFuelFillUpReport(vehicleRow, dtDriverList, StartDate, EndDate, ResuleTable, dtFuelPriceList);
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Generate Fuel Consumption Report
        private void GenerateFuelConsumptionReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtFuelPriceList = _masterData.Tables["FuelPrice"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    ResuleTable = report.GetFuelConsumptionReport(vehicleRow, dtFuelPriceList, dtDriverList, StartDate, EndDate, ResuleTable, Company.TimeFormat);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Generate Fuel Time Report
        private void GenerateFuelTimeGraphReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    bool isCanbusEnabled = Convert.ToBoolean(vehicleRow["IsCanBus"]);
                    if (isCanbusEnabled)
                    {
                        ResuleTable = report.GetFuelVersusTimeGraphReport(vehicleRow, StartDate, EndDate, ResuleTable);
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Generate Fuel Odometer Report
        private void GenerateFuelOdometerGraphReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    bool isCanbusEnabled = Convert.ToBoolean(vehicleRow["IsCanBus"]);
                    if (isCanbusEnabled)
                        ResuleTable = report.GetFuelVersusOdometerGraphReport(vehicleRow, StartDate, EndDate, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Generate Fuel Distance Report
        private void GenerateFuelDistanceGraphReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    bool isCanbusEnabled = Convert.ToBoolean(vehicleRow["IsCanBus"]);
                    if (isCanbusEnabled)
                    {
                        ResuleTable = report.GetFuelVersusDistanceGraphReport(vehicleRow, StartDate, EndDate, ResuleTable);
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "Data Log Report"
        private void GenerateDataLogReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = null;
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                DataSet dsOut = new DataSet();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetDataLog(vehicleRow, vehicleId, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
                dsOut.Tables.Add(ResuleTable);
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "Harsh Acceleration Breaking"
        private void GenerateHarshEventReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetHarshEventReport(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Weekday Summary Report
        private void GenerateWeekDaySummaryReport(DateTime StartDate, DateTime EndDate, string vehicleID, string WeekDays)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtFuelPriceList = _masterData.Tables["FuelPrice"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetFleetsummaryReport(vehicleRow, vehicleId, dtFuelPriceList, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), WeekDays, Company.TimeFormat, ResuleTable, "{0}D:{1}H:{2}M:{3}S",true,false);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "Driver based Trip Summary Report"
        private void GenerateDriverbasedTripSummaryReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtFuelPriceList = _masterData.Tables["FuelPrice"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetDriverBasedTripSummary(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable,
                        dtFuelPriceList, "{0}D:{1}H:{2}M:{3}S", true,false);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Landmark Visited Vehicle
        private void GenerateLandmarkVisitedReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetLandmarkVisited(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Vehicle Visited Landmark
        private void GenerateVehicleVisitedLandmarkReport(DateTime StartDate, DateTime EndDate, string vehicleID, int LandmarkId)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetSingleLandmarkVisited(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, Convert.ToInt32(LandmarkId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion     
        #region Sensor Live Report
        private void GenerateSensorLiveReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetSensorReport(vehicleRow, vehicleId, dtDriverList, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Sensor Detail Report
        private void GenerateSensorHistoryReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    bool IsWiredTempEnabled = Convert.ToBoolean(vehicleRow["IsTempSensor"]);
                    bool IsWirelessSensorEnabled = Convert.ToBoolean(vehicleRow["IsWireLessSensorEnabled"]);
                    if (IsWiredTempEnabled || IsWirelessSensorEnabled)
                        ResuleTable = report.GetSingleTemperatureReport(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region RFID Based Temperature (Single)
        private void GenerateRFIDTempReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtDriverIdCardList = _masterData.Tables["DriverIdCardMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    bool IsWiredTempEnabled = Convert.ToBoolean(vehicleRow["IsTempSensor"]);
                    if (IsWiredTempEnabled)
                        ResuleTable = report.GetRFIDBasedTemperature(vehicleRow, vehicleId, dtDriverList, dtDriverIdCardList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Temperature Tag List
        // Wireless Temperature report, Used Pivot report based on temperature container
        private void GenerateRFIDTempTagReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtVehicleTempTagList = _masterData.Tables["TempTagMaster"];
                DataTable dtDriverIdCardList = _masterData.Tables["DriverIdCardMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    bool IsWirelessSensorEnabled = Convert.ToBoolean(vehicleRow["IsWireLessSensorEnabled"]);
                    if (IsWirelessSensorEnabled)
                        ResuleTable = report.GetRFIDBasedTempTag(vehicleRow, vehicleId, dtDriverList, dtVehicleTempTagList, dtDriverIdCardList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        private void GenerateTemperatureLevelReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable;
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtVehicleTempTagList = _masterData.Tables["TempTagMaster"];
                DataTable dtDriverIdCardList = _masterData.Tables["DriverIdCardMaster"];
                DataTable dtVehicleDoorList = _masterData.Tables["DoorMaster"];
                DataTable dtLandmarkList = _masterData.Tables["LandmarkMaster"];
                DataRow[] _drFilteredVehicleList = dtVehicleList.Select("VehicleId in (" + vehicleID + ")");
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    int vehicleId = Convert.ToInt32(vehicleRow["VehicleId"]);
                    bool IsWirelessSensorEnabled = Convert.ToBoolean(vehicleRow["IsTempTagEnabled"]);
                    bool IsDoorSensorEnabled = Convert.ToBoolean(vehicleRow["IsDoorSensorEnabled"]);
                    if (IsWirelessSensorEnabled)
                    {
                        dsOut = report.GetTemperatureLevelWithDoorStatus(vehicleRow, vehicleId, dtDriverList, dtVehicleTempTagList, dtDriverIdCardList, dtVehicleDoorList, dtLandmarkList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, dsOut);
                        ResuleTable = dsOut.Tables[0];
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Temperature detail
        // Wireless Temperature report, Used Pivot report based on temperature container
        private void GenerateTempTagReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtVehicleTempTagList = _masterData.Tables["TempTagMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    bool IsWirelessSensorEnabled = Convert.ToBoolean(vehicleRow["IsWireLessSensorEnabled"]);
                    if (IsWirelessSensorEnabled)
                        ResuleTable = report.GetTemperatureTagList(vehicleRow, vehicleId, dtDriverList, dtVehicleTempTagList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Temperature Event Log
        private void GenerateTempEventLogReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = null; ;
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                dsOut = new DataSet();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    bool IsWirelessSensorEnabled = Convert.ToBoolean(vehicleRow["IsWireLessSensorEnabled"]);
                    if (IsWirelessSensorEnabled)
                        ResuleTable = report.GetTemperatureAlertLog(vehicleRow, vehicleId, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
                dsOut.Tables.Add(ResuleTable);
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Temperature Limit
        private void GenerateTempLimitReport(DateTime StartDate, DateTime EndDate, string vehicleID, int MinValue, int MaxValue)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtVehicleTempTagList = _masterData.Tables["TempTagMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    bool IsWiredTempEnabled = Convert.ToBoolean(vehicleRow["IsTempSensor"]);
                    bool IsWirelessSensorEnabled = Convert.ToBoolean(vehicleRow["IsWireLessSensorEnabled"]);
                    if (IsWiredTempEnabled || IsWirelessSensorEnabled)
                        ResuleTable = report.GetTemperatureLimitReport(vehicleRow, vehicleId, dtDriverList, dtVehicleTempTagList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable, MinValue, MaxValue);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region RFID Temp Graph
        private void GenerateRFIDbasedTemperatureGraph(DateTime StartDate, DateTime EndDate, string vehicleID, int TypeID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtDriverIdCardList = _masterData.Tables["DriverIdCardMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    bool IsWiredTempEnabled = Convert.ToBoolean(vehicleRow["IsTempSensor"]);
                    if (IsWiredTempEnabled)
                        ResuleTable = report.GetRFIDBasedTemperature(vehicleRow, vehicleId, dtDriverList, dtDriverIdCardList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable, true);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region RFID Temp Graph
        private void GenerateRFIDbasedTemperatureTagGraph(DateTime StartDate, DateTime EndDate, string vehicleID, int TypeID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtDriverIdCardList = _masterData.Tables["DriverIdCardMaster"];
                DataTable dtVehicleTempTagList = _masterData.Tables["TempTagMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    bool IsWirelessSensorEnabled = Convert.ToBoolean(vehicleRow["IsWireLessSensorEnabled"]);
                    if (IsWirelessSensorEnabled)
                        ResuleTable = report.GetRFIDBasedTempTag(vehicleRow, vehicleId, dtDriverList, dtVehicleTempTagList, dtDriverIdCardList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable, true);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Attendance Report
        private void GenerateAttendanceReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtDriverIdCardList = _masterData.Tables["DriverIdCardMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    bool IsRFIDEnabled = Convert.ToBoolean(vehicleRow["IsRFIDEnabled"]);
                    bool IsStudentPunch = Convert.ToBoolean(vehicleRow["IsStudentPunch"]);
                    if (IsRFIDEnabled || IsStudentPunch)
                        ResuleTable = report.GetAttendanceReport(vehicleRow, vehicleId, dtDriverList, dtDriverIdCardList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Detailed Punch Log
        private void GenerateDetailedPunchLogReport(DateTime StartDate, DateTime EndDate)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                ResuleTable = report.GetRFIDPunchWithLog(StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Employee Activity Report
        private void GenerateEmployeeActivityReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable TempTblReport = null;
                foreach (DataRow vehicleRow in dtVehicleList.Rows)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    bool IsRFIDEnabled = Convert.ToBoolean(vehicleRow["IsRFIDEnabled"]);
                    if (IsRFIDEnabled)
                        TempTblReport = report.GetAttendanceLog(vehicleRow, vehicleId, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, TempTblReport);
                }
                DataView dv = new DataView(TempTblReport);
                dv.Sort = "DriverId ASC, TrackTime ASC";
                TempTblReport = dv.ToTable();
                ResuleTable = report.EmployeeActivityReport(ResuleTable, TempTblReport);
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region In/Out Punch Report
        private void GenerateInOutPunchReport(DateTime StartDate, DateTime EndDate)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable TempTblReport = null;
                foreach (DataRow vehicleRow in dtVehicleList.Rows)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    bool IsRFIDEnabled = Convert.ToBoolean(vehicleRow["IsRFIDEnabled"]);
                    if (IsRFIDEnabled)
                        TempTblReport = report.GetAttendanceLog(vehicleRow, vehicleId, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, TempTblReport);
                }
                DataView dv = new DataView(TempTblReport);
                dv.Sort = "DriverId ASC, TrackTime ASC";
                TempTblReport = dv.ToTable();
                ResuleTable = report.GetInOutPunchReport(StartDate, EndDate, ResuleTable, TempTblReport);
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Employee Punch Detail Report
        private void GenerateEmployeePunchDetailReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtEmployeeList = _masterData.Tables["DriverMaster"];
                DataTable dtEmployeeCardList = _masterData.Tables["DriverIdCardMaster"];
                DataTable dtLandmarkList = _masterData.Tables["LandmarkMaster"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    bool IsRFIDEnabled = Convert.ToBoolean(vehicleRow["IsRFIDEnabled"]);
                    bool IsStudentPunch = Convert.ToBoolean(vehicleRow["IsStudentPunch"]);
                    if (IsRFIDEnabled || IsStudentPunch)
                        ResuleTable = report.GetEmployeePunchDetail(vehicleRow, vehicleId, dtEmployeeList, dtEmployeeCardList, dtLandmarkList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region User Login Report
        private void GenerateUserLoginReport(DateTime StartDate, DateTime EndDate)
        {
            try
            {
                DataTable ResuleTable = null;
                dsOut = new DataSet();
                ResuleTable = report.GetSessionLog(StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Convert.ToInt32(Company.UserId), ResuleTable);
                dsOut.Tables.Add(ResuleTable);
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "Service Schedule Report"
        private void GenerateServiceScheduleReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = null;
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataRow[] _drFilteredVehicleList; 
                if (!string.IsNullOrEmpty(vehicleID))
                    _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); 
                else 
                    _drFilteredVehicleList = dtVehicleList.Select();
                dsOut = new DataSet();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetServiceScheduleInfo(vehicleRow, vehicleId, null, null, ResuleTable);
                }
                dsOut.Tables.Add(ResuleTable);
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "Expense Log Report"
        private void GenerateexpenseLogReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = null;
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataRow[] _drFilteredVehicleList; 
                if (!string.IsNullOrEmpty(vehicleID))
                    _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); 
                else 
                    _drFilteredVehicleList = dtVehicleList.Select();
                dsOut = new DataSet();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    ResuleTable = report.GetExpenseLog(vehicleRow, vehicleId, StartDate, EndDate, ResuleTable);
                }
                dsOut.Tables.Add(ResuleTable);
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Driver Job Duration
        private void GenerateDriverJobDurationReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {
                DataTable ResuleTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtDriverIdList = _masterData.Tables["DriverIdCardMaster"];
                DataTable _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")").CopyToDataTable();
                DataRow DriverIdCard = null;
                foreach (DataRow driverRow in dtDriverList.Rows)
                {
                    DriverIdCard = null;
                    if (driverRow["DriverCardId"].ToString() != "")
                    {
                        DataRow[] dataRowDriverResult = dtDriverIdList.Select("DriverCardId = " + driverRow["DriverCardId"].ToString());
                        if (dataRowDriverResult.Length > 0)
                            DriverIdCard = dataRowDriverResult.First();
                    }
                    ResuleTable = report.GetDriverJobSummary(driverRow, _drFilteredVehicleList, DriverIdCard, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, ResuleTable);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region "GCS Report"
        private void GenerateGCSReport(DateTime StartDate, DateTime EndDate, int vehicleID)
        {
            try
            {
                DataTable ResuleTable = null;
                dsOut = new DataSet();
                ResuleTable = report.GetGCSReport(vehicleID, StartDate, EndDate, ResuleTable);
                dsOut.Tables.Add(ResuleTable);
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Tracking Summary Report
        private void GenerateTrackingSummaryReport(DateTime StartDate, DateTime EndDate, string vehicleID)
        {
            try
            {   
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtFuelPriceList = _masterData.Tables["FuelPrice"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    dsOut = report.GetTrackingSummaryReport(vehicleRow, vehicleId, dtDriverList, dtFuelPriceList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), Company.TimeFormat, dsOut);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion
        #region Idle Violation Report
        private void GenerateIdleViolationReport(DateTime StartDate, DateTime EndDate, string vehicleID, int IdleAllowedDuration)
        {
            try
            {
                DataTable ResultTable = dsOut.Tables[0];
                DataTable dtVehicleList = _masterData.Tables["VehicleMaster"];
                DataTable dtDriverList = _masterData.Tables["DriverMaster"];
                DataTable dtFuelPriceList = _masterData.Tables["FuelPrice"];
                DataRow[] _drFilteredVehicleList; if (!string.IsNullOrEmpty(vehicleID)) _drFilteredVehicleList = dtVehicleList.Select("ID in (" + vehicleID + ")"); else _drFilteredVehicleList = dtVehicleList.Select();
                foreach (DataRow vehicleRow in _drFilteredVehicleList)
                {
                    long vehicleId = Convert.ToInt64(vehicleRow["ID"]);
                    vehicleRow["IsCanBus"] = false;
                    ResultTable = report.GetIdleViolationReport(vehicleRow, vehicleId, dtDriverList, StartDate, EndDate, Convert.ToInt64(Company.CompanyId), IdleAllowedDuration, Company.TimeFormat, ResultTable, dtFuelPriceList, "{0}D:{1}H:{2}M:{3}S",true,false);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex); dsOut = null;
            }
        }
        #endregion

    }
}