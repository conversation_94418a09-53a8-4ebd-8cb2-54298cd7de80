﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Web;
using System.Web.Http;
using System.Web.Http.Description;
using VZoneTrack.Gps.V32.WebApi.Interfaces;

namespace VZoneTrack.Gps.V32.WebApi.Controllers
{
    public class ControllerBase : Api<PERSON>ontroller, IVZoneControllerContext
    {
        [ApiExplorerSettings(IgnoreApi = true)]
        public string GetUserDetails()
        {
            ClaimsIdentity identity = User.Identity as ClaimsIdentity;
            if (identity != null)
            {
                IEnumerable<Claim> claims = identity.Claims;
                return claims.ElementAt(0).Value;
            }
            return null;
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public string GetRequestType()
        {
            ClaimsIdentity identity = User.Identity as ClaimsIdentity;
            if (identity != null)
            {
                IEnumerable<Claim> claims = identity.Claims;
                return claims.Where(c => c.Type == "RequestType").First().Value;
            }
            return null;
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public string GetApiType()
        {
            ClaimsIdentity identity = User.Identity as ClaimsIdentity;
            if (identity != null)
            {
                IEnumerable<Claim> claims = identity.Claims;
                return claims.Where(c => c.Type == "ApiType").First().Value;
            }
            return null;
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public string GetTokenType()
        {
            ClaimsIdentity identity = User.Identity as ClaimsIdentity;
            if (identity != null)
            {
                IEnumerable<Claim> claims = identity.Claims;
                return claims.Where(c => c.Type == "TokenType").First().Value;
            }
            return null;
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public bool TwoFactorAuthenticateUser(string username, string apiType)
        {
            bool _isVerified = false;

            if (apiType.ToLower() == "external")
            {
                using (var context = new VZoneTrackEntities())
                {
                    var query = from user in context.Users
                                where user.UserName.ToUpper() == username.ToUpper() && user.HasWebApiAccess == true && user.IsActive == true
                                select user;
                    if (query.Any())
                        _isVerified = true;
                }
            }
            else if (apiType.ToLower() == "internal")
            {
                using (var context = new VZoneTrackEntities())
                {
                    var query = from user in context.V41ApiToken
                                where user.Username.ToUpper() == username.ToUpper() && user.IsActive == true
                                select user;
                    if (query.Any())
                        _isVerified = true;
                }
            }
            return _isVerified;
        }
    }
}