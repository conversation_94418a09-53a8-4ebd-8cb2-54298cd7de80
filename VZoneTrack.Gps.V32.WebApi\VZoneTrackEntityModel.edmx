﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="VZoneTrackModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="AnalyticsTripDetail">
          <Key>
            <PropertyRef Name="TripDetailId" />
          </Key>
          <Property Name="TripDetailId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="TripSummaryId" Type="int" Nullable="false" />
          <Property Name="CompanyId" Type="int" Nullable="false" />
          <Property Name="VehicleId" Type="int" Nullable="false" />
          <Property Name="DriverId" Type="int" />
          <Property Name="StartLocation" Type="nvarchar" MaxLength="500" />
          <Property Name="StartLatitude" Type="decimal" Precision="18" Scale="6" />
          <Property Name="StartLongitude" Type="decimal" Precision="18" Scale="6" />
          <Property Name="StartTime" Type="datetime" />
          <Property Name="EndLocation" Type="nvarchar" MaxLength="500" />
          <Property Name="EndLatitude" Type="decimal" Precision="18" Scale="6" />
          <Property Name="EndLongitude" Type="decimal" Precision="18" Scale="6" />
          <Property Name="EndTime" Type="datetime" />
          <Property Name="TravelDurationMin" Type="int" />
          <Property Name="IdleDurationMin" Type="int" />
          <Property Name="TripDurationMin" Type="int" />
          <Property Name="FuelCons" Type="decimal" Precision="18" Scale="2" />
          <Property Name="FuelCost" Type="decimal" Precision="18" Scale="2" />
          <Property Name="DistanceTraveled" Type="decimal" Precision="18" Scale="2" />
          <Property Name="AlertDetailJson" Type="nvarchar(max)" />
          <Property Name="DoorDetailJson" Type="nvarchar(max)" />
          <Property Name="TemperatureDetailJson" Type="nvarchar(max)" />
          <Property Name="SwipeDetailJson" Type="nvarchar(max)" />
          <Property Name="ChillerDetailJson" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="AnalyticsTripSummary">
          <Key>
            <PropertyRef Name="TripSummaryId" />
          </Key>
          <Property Name="TripSummaryId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CompanyId" Type="int" Nullable="false" />
          <Property Name="VehicleId" Type="int" Nullable="false" />
          <Property Name="TripDate" Type="date" Nullable="false" />
          <Property Name="TopSpeed" Type="decimal" Precision="18" Scale="2" />
          <Property Name="AvgSpeed" Type="decimal" Precision="18" Scale="2" />
          <Property Name="TravelDurationMin" Type="int" />
          <Property Name="IdleDurationMin" Type="int" />
          <Property Name="StopDurationMin" Type="int" />
          <Property Name="TotalDurationMin" Type="int" />
          <Property Name="FuelCons" Type="decimal" Precision="18" Scale="2" />
          <Property Name="FuelCost" Type="decimal" Precision="18" Scale="2" />
          <Property Name="IdleViolationDurationMin" Type="int" />
          <Property Name="IdleViolationFuelCons" Type="decimal" Precision="18" Scale="2" />
          <Property Name="IdleViolationFuelCost" Type="decimal" Precision="18" Scale="2" />
          <Property Name="DistanceTraveled" Type="decimal" Precision="18" Scale="2" />
          <Property Name="AlertSummaryJson" Type="nvarchar(max)" />
          <Property Name="DoorSummaryJson" Type="nvarchar(max)" />
          <Property Name="TemperatureSummaryJson" Type="nvarchar(max)" />
          <Property Name="SwipeSummaryJson" Type="nvarchar(max)" />
          <Property Name="ChillerSummaryJson" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="Category">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CategoryName" Type="varchar" MaxLength="10" />
          <Property Name="CategoryImage" Type="varchar" MaxLength="250" />
          <Property Name="GeometryImage" Type="varchar" MaxLength="50" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="IsDelete" Type="bit" Nullable="false" />
          <Property Name="CreatedBy" Type="bigint" />
          <Property Name="CreatedDate" Type="date" Nullable="false" />
          <Property Name="ModifiedBy" Type="smallint" />
          <Property Name="ModifiedDate" Type="date" Nullable="false" />
        </EntityType>
        <EntityType Name="Company">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Code" Type="varchar" MaxLength="50" />
          <Property Name="CompanyName" Type="varchar" MaxLength="150" Nullable="false" />
          <Property Name="ContactPerson" Type="varchar" MaxLength="50" />
          <Property Name="Address" Type="varchar" MaxLength="250" />
          <Property Name="PhoneNo" Type="varchar" MaxLength="20" />
          <Property Name="MobileNo" Type="varchar" MaxLength="20" />
          <Property Name="Email" Type="varchar" MaxLength="500" />
          <Property Name="Url" Type="varchar" MaxLength="50" />
          <Property Name="IsPartner" Type="bit" Nullable="false" />
          <Property Name="Logo" Type="varchar" MaxLength="255" />
          <Property Name="DBBackupTime" Type="smalldatetime" />
          <Property Name="IsTeleCall" Type="bit" Nullable="false" />
          <Property Name="IsControlCentre" Type="bit" Nullable="false" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="IsDelete" Type="bit" Nullable="false" />
          <Property Name="CreatedBy" Type="bigint" />
          <Property Name="CreatedDate" Type="date" Nullable="false" />
          <Property Name="UpdatedBy" Type="bigint" />
          <Property Name="UpdatedDate" Type="date" Nullable="false" />
          <Property Name="DefaultLat" Type="decimal" Precision="18" Scale="8" Nullable="false" />
          <Property Name="DefaultLong" Type="decimal" Precision="18" Scale="8" Nullable="false" />
          <Property Name="AssCompanyID" Type="varchar" MaxLength="200" />
          <Property Name="DBName" Type="varchar" MaxLength="50" />
          <Property Name="AllowVehicleMultipleGroup" Type="smallint" />
          <Property Name="IsDeliveryAppEnabled" Type="bit" Nullable="false" />
          <Property Name="IsVZone4DEnabled" Type="bit" Nullable="false" />
          <Property Name="DefaultZoomLevel" Type="int" Nullable="false" />
          <Property Name="IsTempDashboardEnabled" Type="bit" Nullable="false" />
          <Property Name="IsFleetInfoSystemEnabled" Type="bit" Nullable="false" />
          <Property Name="IsPremiumAccount" Type="bit" Nullable="false" />
          <Property Name="IsUnderTrial" Type="bit" Nullable="false" />
          <Property Name="TrialExpiryOn" Type="datetime" />
          <Property Name="CustomMessage" Type="nvarchar(max)" />
          <Property Name="CustomMessageExpiryOn" Type="datetime" />
          <Property Name="TimeZone" Type="varchar" MaxLength="10" Nullable="false" />
          <Property Name="HasPassengerTracking" Type="bit" Nullable="false" />
          <Property Name="IsRequireStalledListAlert" Type="bit" Nullable="false" />
          <Property Name="MaxStorageInMB" Type="int" Nullable="false" />
          <Property Name="UtilizedStorageInKB" Type="int" Nullable="false" />
          <Property Name="CancellationReason" Type="nvarchar(max)" />
          <Property Name="HHTConfigEnabled" Type="bit" Nullable="false" />
          <Property Name="HHTCodeRange" Type="varchar" MaxLength="250" />
          <Property Name="IsCaseLevelAuthFeatureActivated" Type="bit" Nullable="false" />
          <Property Name="ApiRateLimit" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="Device">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="DeviceIMEI" />
            <PropertyRef Name="TypeId" />
          </Key>
          <Property Name="ID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="DeviceIMEI" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="ProgrammedOn" Type="datetime" />
          <Property Name="DeviceType" Type="varchar" MaxLength="50" />
          <Property Name="Description" Type="varchar" MaxLength="500" />
          <Property Name="TimeZone" Type="int" />
          <Property Name="MCC" Type="int" />
          <Property Name="MNC" Type="int" />
          <Property Name="SIMNO" Type="varchar" MaxLength="20" />
          <Property Name="SIMIMSI" Type="varchar" MaxLength="30" />
          <Property Name="CountryCode" Type="int" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="IsDelete" Type="bit" Nullable="false" />
          <Property Name="CreatedBy" Type="bigint" />
          <Property Name="CreatedDate" Type="date" Nullable="false" />
          <Property Name="ModifiedBy" Type="bigint" />
          <Property Name="ModifiedDate" Type="date" Nullable="false" />
          <Property Name="Ign_Port" Type="smallint" Nullable="false" />
          <Property Name="IsTraceDataRequired" Type="bit" />
          <Property Name="TypeId" Type="int" Nullable="false" />
          <Property Name="BatchNo" Type="varchar" MaxLength="50" />
          <Property Name="ExpiryDate" Type="datetime" />
          <Property Name="AccessKey" Type="int" />
        </EntityType>
        <EntityType Name="Driver">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="CompanyID" />
          </Key>
          <Property Name="ID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Code" Type="varchar" MaxLength="25" />
          <Property Name="CompanyID" Type="bigint" Nullable="false" />
          <Property Name="DriverName" Type="varchar" MaxLength="250" />
          <Property Name="DellasKey" Type="varchar" MaxLength="10" />
          <Property Name="LicenseNo" Type="varchar" MaxLength="50" />
          <Property Name="LicenseIssueDate" Type="date" />
          <Property Name="LicenseExpiryDate" Type="date" />
          <Property Name="MobileNo" Type="varchar" MaxLength="50" />
          <Property Name="Email" Type="varchar" MaxLength="50" />
          <Property Name="Designation" Type="varchar" MaxLength="50" />
          <Property Name="Picture" Type="varchar" MaxLength="100" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="IsDelete" Type="bit" Nullable="false" />
          <Property Name="CreatedBy" Type="bigint" />
          <Property Name="CreatedDate" Type="datetime" Nullable="false" />
          <Property Name="ModifiedBy" Type="bigint" />
          <Property Name="ModifiedDate" Type="datetime" Nullable="false" />
          <Property Name="IsDriver" Type="bit" Nullable="false" />
          <Property Name="TempCardType" Type="smallint" />
          <Property Name="DriverCardID" Type="int" />
          <Property Name="IDNumber" Type="varchar" MaxLength="50" />
          <Property Name="Nationality" Type="varchar" MaxLength="50" />
          <Property Name="DOB" Type="date" />
          <Property Name="ExpiryDate" Type="date" />
          <Property Name="IssueDate" Type="date" />
          <Property Name="IsSystemDefined" Type="bit" Nullable="false" />
          <Property Name="Remarks" Type="varchar" MaxLength="1024" />
          <Property Name="IsCase_Authenticated" Type="bit" Nullable="false" />
          <Property Name="IsCase_Authenticated_At" Type="datetime" />
          <Property Name="IsCase_Authenticated_RefId" Type="bigint" />
          <Property Name="IsCase_Tokenized" Type="bit" Nullable="false" />
          <Property Name="IsCase_VehiclePersonnelAllocationId" Type="int" />
        </EntityType>
        <EntityType Name="Group">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="CompanyID" />
          </Key>
          <Property Name="ID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Code" Type="varchar" MaxLength="25" />
          <Property Name="CompanyID" Type="bigint" Nullable="false" />
          <Property Name="GroupName" Type="varchar" MaxLength="50" />
          <Property Name="Description" Type="varchar" MaxLength="250" />
          <Property Name="IsSystemDefined" Type="bit" Nullable="false" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="IsDelete" Type="bit" Nullable="false" />
          <Property Name="CreatedBy" Type="bigint" />
          <Property Name="CreatedDate" Type="datetime" Nullable="false" />
          <Property Name="UpdatedBy" Type="bigint" />
          <Property Name="UpdatedDate" Type="datetime" Nullable="false" />
        </EntityType>
        <EntityType Name="LastData">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="VehicleID" Type="bigint" Nullable="false" />
          <Property Name="TrackingID" Type="bigint" Nullable="false" />
          <Property Name="TraceCode" Type="nvarchar" MaxLength="50" />
          <Property Name="Speed" Type="decimal" Precision="18" Scale="2" />
          <Property Name="MovingDirection" Type="decimal" Precision="18" Scale="2" />
          <Property Name="DriverID" Type="bigint" />
          <Property Name="Latitude" Type="decimal" Precision="18" Scale="8" />
          <Property Name="Longitude" Type="decimal" Precision="18" Scale="8" />
          <Property Name="SIMNo" Type="nvarchar" MaxLength="50" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="TrackTime" Type="datetime" />
          <Property Name="ServerTime" Type="datetime" />
          <Property Name="Status" Type="int" />
          <Property Name="ChargeStatus" Type="nvarchar" MaxLength="50" />
          <Property Name="BatteryVoltage" Type="decimal" Precision="18" Scale="2" />
          <Property Name="ChargeVoltage" Type="decimal" Precision="18" Scale="2" />
          <Property Name="ADC0Collect" Type="decimal" Precision="18" Scale="2" />
          <Property Name="ADC1Collect" Type="decimal" Precision="18" Scale="2" />
          <Property Name="FuelSensor" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Temperature" Type="decimal" Precision="18" Scale="2" />
          <Property Name="TotalMilemeter" Type="decimal" Precision="18" Scale="4" />
          <Property Name="CurrentOdometer" Type="decimal" Precision="18" Scale="4" Nullable="false" />
          <Property Name="Job" Type="smallint" />
          <Property Name="LocationName" Type="nvarchar" MaxLength="2000" />
          <Property Name="DoorSensor" Type="int" />
          <Property Name="TrackEvent" Type="nvarchar" MaxLength="2000" />
          <Property Name="SeatBelt" Type="smallint" />
          <Property Name="PDOP" Type="decimal" Precision="18" Scale="1" />
          <Property Name="HDOP" Type="decimal" Precision="18" Scale="1" />
          <Property Name="VDOP" Type="decimal" Precision="18" Scale="1" />
          <Property Name="CompanyID" Type="bigint" />
          <Property Name="LACCIC" Type="varchar" MaxLength="10" />
          <Property Name="LACCIL" Type="varchar" MaxLength="10" />
          <Property Name="FuelTank" Type="smallint" />
          <Property Name="Alarm" Type="varchar" MaxLength="20" />
          <Property Name="GPRMCState" Type="varchar" MaxLength="10" />
          <Property Name="NS" Type="varchar" MaxLength="10" />
          <Property Name="WE" Type="varchar" MaxLength="10" />
          <Property Name="RFID" Type="varchar" MaxLength="15" />
          <Property Name="IsLandmark" Type="bit" />
          <Property Name="HighSpeed" Type="decimal" Precision="18" Scale="2" />
          <Property Name="FirstStartTime" Type="datetime" />
          <Property Name="LandmarkID" Type="bigint" />
          <Property Name="LandmarkName" Type="varchar" MaxLength="100" />
          <Property Name="AwayAt" Type="decimal" Precision="18" Scale="2" />
          <Property Name="TotTravel" Type="int" />
          <Property Name="TotIdle" Type="int" />
          <Property Name="TotStop" Type="int" />
          <Property Name="TempTagList" Type="varchar(max)" />
          <Property Name="IButtonValue" Type="varchar" MaxLength="500" />
          <Property Name="PropertyList" Type="nvarchar(max)" />
          <Property Name="LastEventProcessedTime" Type="datetime" />
        </EntityType>
        <EntityType Name="Users">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="CompanyID" />
          </Key>
          <Property Name="ID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CompanyID" Type="bigint" Nullable="false" />
          <Property Name="FirstName" Type="varchar" MaxLength="50" />
          <Property Name="LastName" Type="varchar" MaxLength="50" />
          <Property Name="UserName" Type="varchar" MaxLength="50" />
          <Property Name="Password" Type="varchar" MaxLength="50" />
          <Property Name="IsAdmin" Type="smallint" Nullable="false" />
          <Property Name="LastLoggedIn" Type="datetime" />
          <Property Name="TotalLogins" Type="int" Nullable="false" />
          <Property Name="IsSystemDefined" Type="bit" Nullable="false" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="IsDeleted" Type="bit" Nullable="false" />
          <Property Name="CreatedBy" Type="bigint" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="bigint" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="IsTrialAccount" Type="bit" />
          <Property Name="TrialExpiry" Type="date" />
          <Property Name="Alerts_ReadTime" Type="datetime" />
          <Property Name="IsOnline" Type="bit" Nullable="false" />
          <Property Name="UserImage" Type="varchar" MaxLength="255" />
          <Property Name="Email" Type="varchar" MaxLength="50" />
          <Property Name="IsForceRebootRequired" Type="bit" Nullable="false" />
          <Property Name="RebootRequestType" Type="smallint" />
          <Property Name="User_Email" Type="varchar" MaxLength="50" />
          <Property Name="MobileNo" Type="varchar" MaxLength="50" />
          <Property Name="Department" Type="varchar" MaxLength="50" />
          <Property Name="Designation" Type="varchar" MaxLength="50" />
          <Property Name="OfficeLocation" Type="varchar" MaxLength="50" />
          <Property Name="Remarks" Type="varchar" MaxLength="1024" />
          <Property Name="IsProfileUpdated" Type="bit" Nullable="false" />
          <Property Name="ProfileUpdatedOn" Type="datetime" />
          <Property Name="IsTrainingRequired" Type="bit" Nullable="false" />
          <Property Name="TrainingGivenOn" Type="datetime" />
          <Property Name="IsMentor" Type="bit" Nullable="false" />
          <Property Name="IsEmailVerified" Type="bit" Nullable="false" />
          <Property Name="IsStalledNotificationReqd" Type="bit" Nullable="false" />
          <Property Name="HasWebApiAccess" Type="bit" />
          <Property Name="AuthenticationLogging" Type="varchar" MaxLength="1024" />
          <Property Name="IsRememberMeEnabled" Type="bit" Nullable="false" />
          <Property Name="RememberMeSetOn" Type="datetime" />
          <Property Name="IsAsateelInfoUpdated" Type="bit" Nullable="false" />
          <Property Name="SecretKey" Type="varchar" MaxLength="20" Nullable="false" />
        </EntityType>
        <EntityType Name="V32DriverIDCard">
          <Key>
            <PropertyRef Name="DriverCardID" />
            <PropertyRef Name="CompanyID" />
          </Key>
          <Property Name="DriverCardID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CompanyID" Type="int" Nullable="false" />
          <Property Name="CardType" Type="smallint" Nullable="false" />
          <Property Name="CardNumber" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Status" Type="smallint" />
          <Property Name="IsActive" Type="bit" />
          <Property Name="CreatedBy" Type="int" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="int" />
          <Property Name="ModifiedDate" Type="datetime" />
        </EntityType>
        <EntityType Name="V32UserGroup">
          <Key>
            <PropertyRef Name="UserGroupId" />
            <PropertyRef Name="UserId" />
            <PropertyRef Name="GroupId" />
          </Key>
          <Property Name="UserGroupId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="UserId" Type="int" Nullable="false" />
          <Property Name="GroupId" Type="int" Nullable="false" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="CreatedBy" Type="bigint" />
          <Property Name="CreatedDate" Type="datetime" Nullable="false" />
          <Property Name="ModifiedBy" Type="bigint" />
          <Property Name="ModifiedDate" Type="datetime" Nullable="false" />
        </EntityType>
        <EntityType Name="V41ApiToken">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="RequestType" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Username" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="PasswordHash" Type="varchar" MaxLength="4000" Nullable="false" />
          <Property Name="PasswordString" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="CreatedDate" Type="datetime" Nullable="false" />
          <Property Name="ModifiedDate" Type="datetime" Nullable="false" />
        </EntityType>
        <EntityType Name="V41HHTLog_AlainFarms">
          <Key>
            <PropertyRef Name="HHTLogId" />
          </Key>
          <Property Name="HHTLogId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="RouteNo" Type="varchar" MaxLength="25" />
          <Property Name="HHTCode" Type="int" Nullable="false" />
          <Property Name="TimeStamp" Type="datetime" />
          <Property Name="Status" Type="smallint" />
          <Property Name="DepotCode" Type="varchar" MaxLength="25" />
          <Property Name="SubArea" Type="varchar" MaxLength="55" />
          <Property Name="Division" Type="varchar" MaxLength="25" />
          <Property Name="Supervisor" Type="varchar" MaxLength="155" />
          <Property Name="Salesman" Type="varchar" MaxLength="155" />
          <Property Name="CreatedOn" Type="datetime" />
        </EntityType>
        <EntityType Name="Vehicle">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="DeviceID" />
          </Key>
          <Property Name="ID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="DeviceID" Type="bigint" Nullable="false" />
          <Property Name="DriverID" Type="bigint" Nullable="false" />
          <Property Name="CategoryID" Type="int" />
          <Property Name="AssetCode" Type="varchar" MaxLength="15" />
          <Property Name="Prefix" Type="varchar" MaxLength="50" />
          <Property Name="DisplayText" Type="nvarchar" MaxLength="50" />
          <Property Name="RegistrationNo" Type="varchar" MaxLength="50" />
          <Property Name="Suffix" Type="varchar" MaxLength="50" />
          <Property Name="PrefixSeperator" Type="char" MaxLength="2" />
          <Property Name="SuffixSeperator" Type="char" MaxLength="2" />
          <Property Name="Make" Type="varchar" MaxLength="50" />
          <Property Name="Model" Type="varchar" MaxLength="50" />
          <Property Name="FuelType" Type="int" Nullable="false" />
          <Property Name="EmptyVoltage" Type="decimal" Precision="18" Scale="2" />
          <Property Name="FullVoltage" Type="decimal" Precision="18" Scale="2" />
          <Property Name="FuelTankCapacity" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="MaxSpeed" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Picture" Type="varchar" MaxLength="100" />
          <Property Name="Comments" Type="varchar" MaxLength="2000" />
          <Property Name="InstallationDate" Type="date" />
          <Property Name="Mileage" Type="int" />
          <Property Name="Odometer" Type="int" />
          <Property Name="IsActive" Type="bit" />
          <Property Name="IsDelete" Type="bit" />
          <Property Name="IsDoorSensor" Type="bit" Nullable="false" />
          <Property Name="IsTempSensor" Type="bit" />
          <Property Name="IsSeatBelt" Type="bit" Nullable="false" />
          <Property Name="IsFuelTank" Type="bit" />
          <Property Name="CreatedBy" Type="bigint" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="bigint" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="IsRFIDEnabled" Type="bit" />
          <Property Name="IsBarcodeEnabled" Type="bit" />
          <Property Name="IsStudentPunch" Type="bit" />
          <Property Name="IdleDuration" Type="int" />
          <Property Name="IsRFIDBasedDriver" Type="smallint" />
          <Property Name="IsSwipeOnStartRequired" Type="bit" />
          <Property Name="IsKMCalcFromField" Type="bit" Nullable="false" />
          <Property Name="IsGadgetInstalled" Type="bit" Nullable="false" />
          <Property Name="IsAuthRFIDType" Type="bit" />
          <Property Name="IsTeZEventDependsCardSwipe" Type="bit" Nullable="false" />
          <Property Name="IsBuzzerEnabled" Type="bit" Nullable="false" />
          <Property Name="IsImmobilization" Type="bit" Nullable="false" />
          <Property Name="IsCanBus" Type="bit" Nullable="false" />
          <Property Name="TravelKMPerLtr" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="IdleConsumptionPerHr" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="EngineSize" Type="decimal" Precision="18" Scale="1" Nullable="false" />
          <Property Name="AllowedIdleDurInMin" Type="int" Nullable="false" />
          <Property Name="AllowedOverspeedDurInMin" Type="int" Nullable="false" />
          <Property Name="AllowedHarshAccCount" Type="int" Nullable="false" />
          <Property Name="AllowedHarshBrkCount" Type="int" Nullable="false" />
          <Property Name="HasTrackerOdometer" Type="bit" Nullable="false" />
          <Property Name="CurrentStalledStatus" Type="smallint" />
          <Property Name="StalledDueDate" Type="date" />
          <Property Name="IsChillerStatusEnabled" Type="bit" Nullable="false" />
          <Property Name="ChillerInputPort" Type="smallint" />
          <Property Name="JobIdentificationType" Type="smallint" />
          <Property Name="IsDOUTControlRequired" Type="bit" Nullable="false" />
          <Property Name="DOUTControlPort" Type="smallint" />
          <Property Name="CurrentDOUTControlStatus" Type="bit" Nullable="false" />
          <Property Name="HasTempCardPunch" Type="bit" Nullable="false" />
          <Property Name="Odometer_SetOn" Type="datetime" />
          <Property Name="ChillerTimeInMins" Type="int" Nullable="false" />
          <Property Name="ChillerTime_SetOn" Type="datetime" Nullable="false" />
          <Property Name="Chiller_Remarks" Type="varchar" MaxLength="1024" />
          <Property Name="VIN" Type="varchar" MaxLength="50" />
          <Property Name="ManufactureYear" Type="int" />
          <Property Name="Trim" Type="varchar" MaxLength="50" />
          <Property Name="Color" Type="varchar" MaxLength="50" />
          <Property Name="MSRP" Type="decimal" Precision="18" Scale="1" />
          <Property Name="EstimatedMonth" Type="int" />
          <Property Name="EstimatedKM" Type="int" />
          <Property Name="ResaleValue" Type="decimal" Precision="18" Scale="1" />
          <Property Name="IsEngineIdleEnabled" Type="bit" Nullable="false" />
          <Property Name="ThresholdSupplyVoltage" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="ConsDriftingDataOmitCount" Type="int" Nullable="false" />
          <Property Name="IsDefaultDriftingFilterEnabled" Type="bit" Nullable="false" />
          <Property Name="IsCaseLevelAuthEnabled" Type="bit" Nullable="false" />
          <Property Name="IsCase_Authenticated" Type="bit" Nullable="false" />
          <Property Name="IsTrailer" Type="bit" Nullable="false" />
          <Property Name="IsHead" Type="bit" Nullable="false" />
          <Property Name="IsAttendanceDetailRequired" Type="bit" Nullable="false" />
          <Property Name="AnalyticsJson" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="VehicleGroup">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="VehicleID" />
            <PropertyRef Name="GroupID" />
          </Key>
          <Property Name="ID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="VehicleID" Type="bigint" Nullable="false" />
          <Property Name="GroupID" Type="bigint" Nullable="false" />
          <Property Name="IsDefault" Type="bit" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="IsDelete" Type="bit" Nullable="false" />
          <Property Name="CreatedBy" Type="bigint" />
          <Property Name="CreatedDate" Type="date" Nullable="false" />
          <Property Name="ModifiedBy" Type="bigint" />
          <Property Name="ModifiedDate" Type="date" Nullable="false" />
        </EntityType>
        <Function Name="udf_GetLastTime" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="true" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" ReturnType="varchar">
          <Parameter Name="date" Type="datetime" Mode="In" />
          <Parameter Name="deviceID" Type="int" Mode="In" />
        </Function>
        <Function Name="usp_GetVehiclesDetails" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="in_CompanyId" Type="bigint" Mode="In" />
          <Parameter Name="in_AssetCode" Type="varchar" Mode="In" />
        </Function>
        <Function Name="usp_V32GetVehiclesInCompany" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="IN_CompanyId" Type="int" Mode="In" />
          <Parameter Name="IN_FilterJson" Type="varchar(max)" Mode="In" />
        </Function>
        <Function Name="usp_V32GetVehicleTimeZone" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="in_VehicleId" Type="int" Mode="In" />
        </Function>
        <Function Name="usp_V41GetApiToken" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="in_Username" Type="varchar" Mode="In" />
        </Function>
        <Function Name="usp_V41GetCompanyVehicles" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="in_CompanyId" Type="bigint" Mode="In" />
          <Parameter Name="in_UserId" Type="int" Mode="In" />
          <Parameter Name="in_strGroups" Type="varchar" Mode="In" />
        </Function>
        <EntityContainer Name="VZoneTrackModelStoreContainer">
          <EntitySet Name="AnalyticsTripDetail" EntityType="Self.AnalyticsTripDetail" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="AnalyticsTripSummary" EntityType="Self.AnalyticsTripSummary" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Category" EntityType="Self.Category" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Company" EntityType="Self.Company" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Device" EntityType="Self.Device" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Driver" EntityType="Self.Driver" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Group" EntityType="Self.Group" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="LastData" EntityType="Self.LastData" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Users" EntityType="Self.Users" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="V32DriverIDCard" EntityType="Self.V32DriverIDCard" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="V32UserGroup" EntityType="Self.V32UserGroup" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="V41ApiToken" EntityType="Self.V41ApiToken" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="V41HHTLog_AlainFarms" EntityType="Self.V41HHTLog_AlainFarms" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Vehicle" EntityType="Self.Vehicle" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="VehicleGroup" EntityType="Self.VehicleGroup" Schema="dbo" store:Type="Tables" />
        </EntityContainer>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="VZoneTrackModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="User">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="CompanyID" />
          </Key>
          <Property Name="ID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CompanyID" Type="Int64" Nullable="false" />
          <Property Name="FirstName" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="LastName" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="UserName" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Password" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="IsAdmin" Type="Int16" Nullable="false" />
          <Property Name="LastLoggedIn" Type="DateTime" Precision="3" />
          <Property Name="TotalLogins" Type="Int32" Nullable="false" />
          <Property Name="IsSystemDefined" Type="Boolean" Nullable="false" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="IsDeleted" Type="Boolean" Nullable="false" />
          <Property Name="CreatedBy" Type="Int64" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="Int64" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="IsTrialAccount" Type="Boolean" />
          <Property Name="TrialExpiry" Type="DateTime" Precision="0" />
          <Property Name="Alerts_ReadTime" Type="DateTime" Precision="3" />
          <Property Name="IsOnline" Type="Boolean" Nullable="false" />
          <Property Name="UserImage" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="Email" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="IsForceRebootRequired" Type="Boolean" Nullable="false" />
          <Property Name="RebootRequestType" Type="Int16" />
          <Property Name="User_Email" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="MobileNo" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Department" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Designation" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="OfficeLocation" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Remarks" Type="String" MaxLength="1024" FixedLength="false" Unicode="false" />
          <Property Name="IsProfileUpdated" Type="Boolean" Nullable="false" />
          <Property Name="ProfileUpdatedOn" Type="DateTime" Precision="3" />
          <Property Name="IsTrainingRequired" Type="Boolean" Nullable="false" />
          <Property Name="TrainingGivenOn" Type="DateTime" Precision="3" />
          <Property Name="IsMentor" Type="Boolean" Nullable="false" />
          <Property Name="IsEmailVerified" Type="Boolean" Nullable="false" />
          <Property Name="IsStalledNotificationReqd" Type="Boolean" Nullable="false" />
          <Property Name="HasWebApiAccess" Type="Boolean" />
          <Property Name="AuthenticationLogging" Type="String" MaxLength="1024" FixedLength="false" Unicode="false" />
          <Property Name="IsRememberMeEnabled" Type="Boolean" Nullable="false" />
          <Property Name="RememberMeSetOn" Type="DateTime" Precision="3" />
          <Property Name="IsAsateelInfoUpdated" Type="Boolean" Nullable="false" />
          <Property Name="SecretKey" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="V41ApiToken">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="RequestType" Type="String" MaxLength="50" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Username" Type="String" MaxLength="20" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="PasswordHash" Type="String" MaxLength="4000" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="PasswordString" Type="String" MaxLength="50" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="CreatedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="ModifiedDate" Type="DateTime" Nullable="false" Precision="3" />
        </EntityType>
        <EntityContainer Name="VZoneTrackEntities" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="Users" EntityType="Self.User" />
          <EntitySet Name="V41ApiToken" EntityType="Self.V41ApiToken" />
          <FunctionImport Name="usp_V41GetApiToken" ReturnType="Collection(VZoneTrackModel.usp_V41GetApiToken_Result)">
          <Parameter Name="in_Username" Mode="In" Type="String" />
          </FunctionImport>
          <EntitySet Name="Vehicle" EntityType="VZoneTrackModel.Vehicle" />
          <FunctionImport Name="usp_V32GetVehicleTimeZone" ReturnType="Collection(Int32)">
          <Parameter Name="in_VehicleId" Mode="In" Type="Int32" />
          </FunctionImport>
          <EntitySet Name="V41HHTLog_AlainFarms" EntityType="VZoneTrackModel.V41HHTLog_AlainFarms" />
          <EntitySet Name="Company" EntityType="VZoneTrackModel.Company" />
          <EntitySet Name="Driver" EntityType="VZoneTrackModel.Driver" />
          <EntitySet Name="V32DriverIDCard" EntityType="VZoneTrackModel.V32DriverIDCard" />
          <FunctionImport Name="usp_V32GetVehiclesInCompany">
            <Parameter Name="IN_CompanyId" Mode="In" Type="Int32" />
            <Parameter Name="IN_FilterJson" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="GeVehiclesInCompany" ReturnType="Collection(VZoneTrackModel.VehiclesInCompany)">
            <Parameter Name="IN_CompanyId" Mode="In" Type="Int32" />
            <Parameter Name="IN_FilterJson" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="usp_V41GetCompanyVehicles">
            <Parameter Name="in_CompanyId" Mode="In" Type="Int64" />
            <Parameter Name="in_UserId" Mode="In" Type="Int32" />
            <Parameter Name="in_strGroups" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="usp_GetVehiclesDetails">
            <Parameter Name="in_CompanyId" Mode="In" Type="Int64" />
            <Parameter Name="in_AssetCode" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="GetVehiclesDetails" ReturnType="Collection(VZoneTrackModel.VehiclesInfoComplex)">
            <Parameter Name="in_CompanyId" Mode="In" Type="Int64" />
            <Parameter Name="in_AssetCode" Mode="In" Type="String" />
          </FunctionImport>          
          <EntitySet Name="AnalyticsTripDetail" EntityType="VZoneTrackModel.AnalyticsTripDetail" />
          <EntitySet Name="AnalyticsTripSummary" EntityType="VZoneTrackModel.AnalyticsTripSummary" />
          <EntitySet Name="LastData" EntityType="VZoneTrackModel.LastData" />
          <EntitySet Name="Group" EntityType="VZoneTrackModel.Group" />
          <EntitySet Name="V32UserGroup" EntityType="VZoneTrackModel.V32UserGroup" />
          <EntitySet Name="Category" EntityType="VZoneTrackModel.Category" />
          <EntitySet Name="Device" EntityType="VZoneTrackModel.Device" />
          <EntitySet Name="VehicleGroup" EntityType="VZoneTrackModel.VehicleGroup" />
        </EntityContainer>
        <ComplexType Name="usp_V41GetApiToken_Result">
          <Property Type="Int32" Name="Id" Nullable="false" />
          <Property Type="String" Name="RequestType" Nullable="false" MaxLength="50" />
          <Property Type="String" Name="Username" Nullable="false" MaxLength="20" />
          <Property Type="String" Name="PasswordHash" Nullable="false" MaxLength="4000" />
          <Property Type="String" Name="PasswordString" Nullable="false" MaxLength="50" />
          <Property Type="Boolean" Name="IsActive" Nullable="false" />
          <Property Type="DateTime" Name="CreatedDate" Nullable="false" Precision="23" />
          <Property Type="DateTime" Name="ModifiedDate" Nullable="false" Precision="23" />
        </ComplexType>
        <EntityType Name="Vehicle">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="DeviceID" />
          </Key>
          <Property Name="ID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="DeviceID" Type="Int64" Nullable="false" />
          <Property Name="DriverID" Type="Int64" Nullable="false" />
          <Property Name="CategoryID" Type="Int32" />
          <Property Name="AssetCode" Type="String" MaxLength="15" FixedLength="false" Unicode="false" />
          <Property Name="Prefix" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="DisplayText" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="RegistrationNo" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Suffix" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="PrefixSeperator" Type="String" MaxLength="2" FixedLength="true" Unicode="false" />
          <Property Name="SuffixSeperator" Type="String" MaxLength="2" FixedLength="true" Unicode="false" />
          <Property Name="Make" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Model" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="FuelType" Type="Int32" Nullable="false" />
          <Property Name="EmptyVoltage" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="FullVoltage" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="FuelTankCapacity" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="MaxSpeed" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Picture" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="Comments" Type="String" MaxLength="2000" FixedLength="false" Unicode="false" />
          <Property Name="InstallationDate" Type="DateTime" Precision="0" />
          <Property Name="Mileage" Type="Int32" />
          <Property Name="Odometer" Type="Int32" />
          <Property Name="IsActive" Type="Boolean" />
          <Property Name="IsDelete" Type="Boolean" />
          <Property Name="IsDoorSensor" Type="Boolean" Nullable="false" />
          <Property Name="IsTempSensor" Type="Boolean" />
          <Property Name="IsSeatBelt" Type="Boolean" Nullable="false" />
          <Property Name="IsFuelTank" Type="Boolean" />
          <Property Name="CreatedBy" Type="Int64" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="Int64" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="IsRFIDEnabled" Type="Boolean" />
          <Property Name="IsBarcodeEnabled" Type="Boolean" />
          <Property Name="IsStudentPunch" Type="Boolean" />
          <Property Name="IdleDuration" Type="Int32" />
          <Property Name="IsRFIDBasedDriver" Type="Int16" />
          <Property Name="IsSwipeOnStartRequired" Type="Boolean" />
          <Property Name="IsKMCalcFromField" Type="Boolean" Nullable="false" />
          <Property Name="IsGadgetInstalled" Type="Boolean" Nullable="false" />
          <Property Name="IsAuthRFIDType" Type="Boolean" />
          <Property Name="IsTeZEventDependsCardSwipe" Type="Boolean" Nullable="false" />
          <Property Name="IsBuzzerEnabled" Type="Boolean" Nullable="false" />
          <Property Name="IsImmobilization" Type="Boolean" Nullable="false" />
          <Property Name="IsCanBus" Type="Boolean" Nullable="false" />
          <Property Name="TravelKMPerLtr" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="IdleConsumptionPerHr" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="EngineSize" Type="Decimal" Nullable="false" Precision="18" Scale="1" />
          <Property Name="AllowedIdleDurInMin" Type="Int32" Nullable="false" />
          <Property Name="AllowedOverspeedDurInMin" Type="Int32" Nullable="false" />
          <Property Name="AllowedHarshAccCount" Type="Int32" Nullable="false" />
          <Property Name="AllowedHarshBrkCount" Type="Int32" Nullable="false" />
          <Property Name="HasTrackerOdometer" Type="Boolean" Nullable="false" />
          <Property Name="CurrentStalledStatus" Type="Int16" />
          <Property Name="StalledDueDate" Type="DateTime" Precision="0" />
          <Property Name="IsChillerStatusEnabled" Type="Boolean" Nullable="false" />
          <Property Name="ChillerInputPort" Type="Int16" />
          <Property Name="JobIdentificationType" Type="Int16" />
          <Property Name="IsDOUTControlRequired" Type="Boolean" Nullable="false" />
          <Property Name="DOUTControlPort" Type="Int16" />
          <Property Name="CurrentDOUTControlStatus" Type="Boolean" Nullable="false" />
          <Property Name="HasTempCardPunch" Type="Boolean" Nullable="false" />
          <Property Name="Odometer_SetOn" Type="DateTime" Precision="3" />
          <Property Name="ChillerTimeInMins" Type="Int32" Nullable="false" />
          <Property Name="ChillerTime_SetOn" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Chiller_Remarks" Type="String" MaxLength="1024" FixedLength="false" Unicode="false" />
          <Property Name="VIN" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ManufactureYear" Type="Int32" />
          <Property Name="Trim" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Color" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="MSRP" Type="Decimal" Precision="18" Scale="1" />
          <Property Name="EstimatedMonth" Type="Int32" />
          <Property Name="EstimatedKM" Type="Int32" />
          <Property Name="ResaleValue" Type="Decimal" Precision="18" Scale="1" />
          <Property Name="IsEngineIdleEnabled" Type="Boolean" Nullable="false" />
          <Property Name="ThresholdSupplyVoltage" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="ConsDriftingDataOmitCount" Type="Int32" Nullable="false" />
          <Property Name="IsDefaultDriftingFilterEnabled" Type="Boolean" Nullable="false" />
          <Property Name="IsCaseLevelAuthEnabled" Type="Boolean" Nullable="false" />
          <Property Name="IsCase_Authenticated" Type="Boolean" Nullable="false" />
          <Property Name="IsTrailer" Type="Boolean" Nullable="false" />
          <Property Name="IsHead" Type="Boolean" Nullable="false" />
          <Property Name="IsAttendanceDetailRequired" Type="Boolean" Nullable="false" />
          <Property Name="AnalyticsJson" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="V41HHTLog_AlainFarms">
          <Key>
            <PropertyRef Name="HHTLogId" />
          </Key>
          <Property Name="HHTLogId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="RouteNo" Type="String" MaxLength="25" FixedLength="false" Unicode="false" />
          <Property Name="HHTCode" Type="Int32" Nullable="false" />
          <Property Name="TimeStamp" Type="DateTime" Precision="3" />
          <Property Name="Status" Type="Int16" />
          <Property Name="DepotCode" Type="String" MaxLength="25" FixedLength="false" Unicode="false" />
          <Property Name="SubArea" Type="String" MaxLength="55" FixedLength="false" Unicode="false" />
          <Property Name="Division" Type="String" MaxLength="25" FixedLength="false" Unicode="false" />
          <Property Name="Supervisor" Type="String" MaxLength="155" FixedLength="false" Unicode="false" />
          <Property Name="Salesman" Type="String" MaxLength="155" FixedLength="false" Unicode="false" />
          <Property Name="CreatedOn" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="Company">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Code" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="CompanyName" Type="String" Nullable="false" MaxLength="150" FixedLength="false" Unicode="false" />
          <Property Name="ContactPerson" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Address" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="PhoneNo" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="MobileNo" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="Email" Type="String" MaxLength="500" FixedLength="false" Unicode="false" />
          <Property Name="Url" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="IsPartner" Type="Boolean" Nullable="false" />
          <Property Name="Logo" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="DBBackupTime" Type="DateTime" Precision="0" />
          <Property Name="IsTeleCall" Type="Boolean" Nullable="false" />
          <Property Name="IsControlCentre" Type="Boolean" Nullable="false" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="IsDelete" Type="Boolean" Nullable="false" />
          <Property Name="CreatedBy" Type="Int64" />
          <Property Name="CreatedDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="UpdatedBy" Type="Int64" />
          <Property Name="UpdatedDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="DefaultLat" Type="Decimal" Nullable="false" Precision="18" Scale="8" />
          <Property Name="DefaultLong" Type="Decimal" Nullable="false" Precision="18" Scale="8" />
          <Property Name="AssCompanyID" Type="String" MaxLength="200" FixedLength="false" Unicode="false" />
          <Property Name="DBName" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="AllowVehicleMultipleGroup" Type="Int16" />
          <Property Name="IsDeliveryAppEnabled" Type="Boolean" Nullable="false" />
          <Property Name="IsVZone4DEnabled" Type="Boolean" Nullable="false" />
          <Property Name="DefaultZoomLevel" Type="Int32" Nullable="false" />
          <Property Name="IsTempDashboardEnabled" Type="Boolean" Nullable="false" />
          <Property Name="IsFleetInfoSystemEnabled" Type="Boolean" Nullable="false" />
          <Property Name="IsPremiumAccount" Type="Boolean" Nullable="false" />
          <Property Name="IsUnderTrial" Type="Boolean" Nullable="false" />
          <Property Name="TrialExpiryOn" Type="DateTime" Precision="3" />
          <Property Name="CustomMessage" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="CustomMessageExpiryOn" Type="DateTime" Precision="3" />
          <Property Name="TimeZone" Type="String" Nullable="false" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="HasPassengerTracking" Type="Boolean" Nullable="false" />
          <Property Name="IsRequireStalledListAlert" Type="Boolean" Nullable="false" />
          <Property Name="MaxStorageInMB" Type="Int32" Nullable="false" />
          <Property Name="UtilizedStorageInKB" Type="Int32" Nullable="false" />
          <Property Name="CancellationReason" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="HHTConfigEnabled" Type="Boolean" Nullable="false" />
          <Property Name="HHTCodeRange" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="IsCaseLevelAuthFeatureActivated" Type="Boolean" Nullable="false" />
          <Property Name="ApiRateLimit" Type="Int32" Nullable="false" />
        </EntityType>
        <EntityType Name="Driver">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="CompanyID" />
          </Key>
          <Property Name="ID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Code" Type="String" MaxLength="25" FixedLength="false" Unicode="false" />
          <Property Name="CompanyID" Type="Int64" Nullable="false" />
          <Property Name="DriverName" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="DellasKey" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="LicenseNo" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="LicenseIssueDate" Type="DateTime" Precision="0" />
          <Property Name="LicenseExpiryDate" Type="DateTime" Precision="0" />
          <Property Name="MobileNo" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Email" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Designation" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Picture" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="IsDelete" Type="Boolean" Nullable="false" />
          <Property Name="CreatedBy" Type="Int64" />
          <Property Name="CreatedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="ModifiedBy" Type="Int64" />
          <Property Name="ModifiedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="IsDriver" Type="Boolean" Nullable="false" />
          <Property Name="TempCardType" Type="Int16" />
          <Property Name="DriverCardID" Type="Int32" />
          <Property Name="IDNumber" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Nationality" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="DOB" Type="DateTime" Precision="0" />
          <Property Name="ExpiryDate" Type="DateTime" Precision="0" />
          <Property Name="IssueDate" Type="DateTime" Precision="0" />
          <Property Name="IsSystemDefined" Type="Boolean" Nullable="false" />
          <Property Name="Remarks" Type="String" MaxLength="1024" FixedLength="false" Unicode="false" />
          <Property Name="IsCase_Authenticated" Type="Boolean" Nullable="false" />
          <Property Name="IsCase_Authenticated_At" Type="DateTime" Precision="3" />
          <Property Name="IsCase_Authenticated_RefId" Type="Int64" />
          <Property Name="IsCase_Tokenized" Type="Boolean" Nullable="false" />
          <Property Name="IsCase_VehiclePersonnelAllocationId" Type="Int32" />
        </EntityType>
        <EntityType Name="V32DriverIDCard">
          <Key>
            <PropertyRef Name="DriverCardID" />
            <PropertyRef Name="CompanyID" />
          </Key>
          <Property Name="DriverCardID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CompanyID" Type="Int32" Nullable="false" />
          <Property Name="CardType" Type="Int16" Nullable="false" />
          <Property Name="CardNumber" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Status" Type="Int16" />
          <Property Name="IsActive" Type="Boolean" />
          <Property Name="CreatedBy" Type="Int32" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="Int32" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
        </EntityType>
        <ComplexType Name="VehiclesInCompany">
          <Property Type="Int32" Name="Id" Nullable="false" />
          <Property Type="Int32" Name="VehicleId" Nullable="true" />
          <Property Type="String" Name="AssetCode" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="DisplayText" Nullable="true" MaxLength="100" />
          <Property Type="String" Name="RegistrationNo" Nullable="true" MaxLength="100" />
          <Property Type="String" Name="Make" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="Model" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="FuelType" Nullable="true" MaxLength="50" />
          <Property Type="Decimal" Name="FuelTankCapacity" Nullable="true" Precision="18" Scale="2" />
          <Property Type="Int32" Name="AllowedSpeed" Nullable="true" />
          <Property Type="DateTime" Name="InstallationDate" Nullable="true" />
          <Property Type="Int32" Name="InitialOdometer" Nullable="true" />
          <Property Type="DateTime" Name="InitialOdometerSetDate" Nullable="true" />
          <Property Type="Boolean" Name="TempSensorEnabled" Nullable="true" />
          <Property Type="Boolean" Name="DoorSensorEnabled" Nullable="true" />
          <Property Type="Boolean" Name="CanBusEnabled" Nullable="true" />
          <Property Type="Boolean" Name="ChillerEnabled" Nullable="true" />
          <Property Type="Boolean" Name="EngineIdleDetectEnabled" Nullable="true" />
          <Property Type="Boolean" Name="CaseAuthEnabled" Nullable="true" />
          <Property Type="Decimal" Name="TravelKMPerLitre" Nullable="true" Precision="18" Scale="2" />
          <Property Type="Decimal" Name="IdleConsPerLitre" Nullable="true" Precision="18" Scale="2" />
          <Property Type="Boolean" Name="IsActive" Nullable="true" />
        </ComplexType>
        <ComplexType Name="VehiclesInfoComplex">
          <Property Type="Int32" Name="Id" Nullable="false" />
          <Property Type="Int64" Name="VehicleId" Nullable="true" />
          <Property Type="Int64" Name="DeviceId" Nullable="true" />
          <Property Type="Int32" Name="TypeId" Nullable="true" />
          <Property Type="String" Name="DeviceIMEI" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="SIMNO" Nullable="true" MaxLength="50" />
          <Property Type="Int32" Name="ManufactureId" Nullable="true" />
          <Property Type="String" Name="AssetCode" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="DisplayText" Nullable="true" MaxLength="100" />
          <Property Type="String" Name="PlateNo" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="Model" Nullable="true" MaxLength="50" />
          <Property Type="Decimal" Name="MaxSpeed" Nullable="true" Precision="18" Scale="2" />
          <Property Type="Int32" Name="CategoryId" Nullable="true" />
          <Property Type="String" Name="CategoryName" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="CategoryImage" Nullable="true" MaxLength="50" />
          <Property Type="Boolean" Name="IsTempSensor" Nullable="true" />
          <Property Type="Boolean" Name="IsRFIDEnabled" Nullable="true" />
          <Property Type="Boolean" Name="IsDoorSensor" Nullable="true" />
          <Property Type="Boolean" Name="IsSeatbelt" Nullable="true" />
          <Property Type="Boolean" Name="IsAnyTemperatureEnabled" Nullable="true" />
          <Property Type="Int32" Name="IdleDataInterval" Nullable="true" />
          <Property Type="Int32" Name="TimeZone" Nullable="true" />
          <Property Type="Boolean" Name="IsGadgetInstalled" Nullable="true" />
          <Property Type="Boolean" Name="IsCANBUSEnabled" Nullable="true" />
          <Property Type="Decimal" Name="FuelTankCapacity" Nullable="true" Precision="18" Scale="2" />
          <Property Type="Int16" Name="CurrentStalledStatus" Nullable="true" />
          <Property Type="Int16" Name="ChillerInputPort" Nullable="true" />
          <Property Type="Boolean" Name="IsChillerStatusEnabled" Nullable="true" />
          <Property Type="Boolean" Name="IsEngineIdleEnabled" Nullable="true" />
          <Property Type="Decimal" Name="ThresholdSupplyVoltage" Nullable="true" Precision="18" Scale="2" />
          <Property Type="Int32" Name="ConsDriftingDataOmitCount" Nullable="true" />
          <Property Type="Boolean" Name="IsDefaultDriftingFilterEnabled" Nullable="true" />
          <Property Type="Boolean" Name="IsDoorSensorEnabled" Nullable="true" />
          <Property Type="String" Name="FuelType" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="Color" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="VIN" Nullable="true" MaxLength="50" />
          <Property Type="Int32" Name="ManufactureYear" Nullable="true" />
          <Property Type="Decimal" Name="MSRP" Nullable="true" Precision="18" Scale="2" />
          <Property Type="String" Name="VehicleTrim" Nullable="true" MaxLength="50" />
          <Property Type="DateTime" Name="InstallationDate" Nullable="true" Precision="23" />
          <Property Type="Boolean" Name="IsBuzzerEnabled" Nullable="true" />
          <Property Type="Boolean" Name="IsImmobilizationEnabled" Nullable="true" />
        </ComplexType>
        <EntityType Name="AnalyticsTripDetail">
          <Key>
            <PropertyRef Name="TripDetailId" />
          </Key>
          <Property Name="TripDetailId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="TripSummaryId" Type="Int32" Nullable="false" />
          <Property Name="CompanyId" Type="Int32" Nullable="false" />
          <Property Name="VehicleId" Type="Int32" Nullable="false" />
          <Property Name="DriverId" Type="Int32" />
          <Property Name="StartLocation" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="StartLatitude" Type="Decimal" Precision="18" Scale="6" />
          <Property Name="StartLongitude" Type="Decimal" Precision="18" Scale="6" />
          <Property Name="StartTime" Type="DateTime" Precision="3" />
          <Property Name="EndLocation" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="EndLatitude" Type="Decimal" Precision="18" Scale="6" />
          <Property Name="EndLongitude" Type="Decimal" Precision="18" Scale="6" />
          <Property Name="EndTime" Type="DateTime" Precision="3" />
          <Property Name="TravelDurationMin" Type="Int32" />
          <Property Name="IdleDurationMin" Type="Int32" />
          <Property Name="TripDurationMin" Type="Int32" />
          <Property Name="FuelCons" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="FuelCost" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="DistanceTraveled" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="AlertDetailJson" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DoorDetailJson" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="TemperatureDetailJson" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="SwipeDetailJson" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ChillerDetailJson" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="AnalyticsTripSummary">
          <Key>
            <PropertyRef Name="TripSummaryId" />
          </Key>
          <Property Name="TripSummaryId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CompanyId" Type="Int32" Nullable="false" />
          <Property Name="VehicleId" Type="Int32" Nullable="false" />
          <Property Name="TripDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="TopSpeed" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="AvgSpeed" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="TravelDurationMin" Type="Int32" />
          <Property Name="IdleDurationMin" Type="Int32" />
          <Property Name="StopDurationMin" Type="Int32" />
          <Property Name="TotalDurationMin" Type="Int32" />
          <Property Name="FuelCons" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="FuelCost" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="IdleViolationDurationMin" Type="Int32" />
          <Property Name="IdleViolationFuelCons" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="IdleViolationFuelCost" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="DistanceTraveled" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="AlertSummaryJson" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DoorSummaryJson" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="TemperatureSummaryJson" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="SwipeSummaryJson" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ChillerSummaryJson" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="LastData">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="VehicleID" Type="Int64" Nullable="false" />
          <Property Name="TrackingID" Type="Int64" Nullable="false" />
          <Property Name="TraceCode" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Speed" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="MovingDirection" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="DriverID" Type="Int64" />
          <Property Name="Latitude" Type="Decimal" Precision="18" Scale="8" />
          <Property Name="Longitude" Type="Decimal" Precision="18" Scale="8" />
          <Property Name="SIMNo" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="TrackTime" Type="DateTime" Precision="3" />
          <Property Name="ServerTime" Type="DateTime" Precision="3" />
          <Property Name="Status" Type="Int32" />
          <Property Name="ChargeStatus" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="BatteryVoltage" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="ChargeVoltage" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="ADC0Collect" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="ADC1Collect" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="FuelSensor" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Temperature" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="TotalMilemeter" Type="Decimal" Precision="18" Scale="4" />
          <Property Name="CurrentOdometer" Type="Decimal" Nullable="false" Precision="18" Scale="4" />
          <Property Name="Job" Type="Int16" />
          <Property Name="LocationName" Type="String" MaxLength="2000" FixedLength="false" Unicode="true" />
          <Property Name="DoorSensor" Type="Int32" />
          <Property Name="TrackEvent" Type="String" MaxLength="2000" FixedLength="false" Unicode="true" />
          <Property Name="SeatBelt" Type="Int16" />
          <Property Name="PDOP" Type="Decimal" Precision="18" Scale="1" />
          <Property Name="HDOP" Type="Decimal" Precision="18" Scale="1" />
          <Property Name="VDOP" Type="Decimal" Precision="18" Scale="1" />
          <Property Name="CompanyID" Type="Int64" />
          <Property Name="LACCIC" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="LACCIL" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="FuelTank" Type="Int16" />
          <Property Name="Alarm" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="GPRMCState" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="NS" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="WE" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="RFID" Type="String" MaxLength="15" FixedLength="false" Unicode="false" />
          <Property Name="IsLandmark" Type="Boolean" />
          <Property Name="HighSpeed" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="FirstStartTime" Type="DateTime" Precision="3" />
          <Property Name="LandmarkID" Type="Int64" />
          <Property Name="LandmarkName" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="AwayAt" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="TotTravel" Type="Int32" />
          <Property Name="TotIdle" Type="Int32" />
          <Property Name="TotStop" Type="Int32" />
          <Property Name="TempTagList" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Name="IButtonValue" Type="String" MaxLength="500" FixedLength="false" Unicode="false" />
          <Property Name="PropertyList" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="LastEventProcessedTime" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="Group">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="CompanyID" />
          </Key>
          <Property Name="ID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Code" Type="String" MaxLength="25" FixedLength="false" Unicode="false" />
          <Property Name="CompanyID" Type="Int64" Nullable="false" />
          <Property Name="GroupName" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Description" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="IsSystemDefined" Type="Boolean" Nullable="false" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="IsDelete" Type="Boolean" Nullable="false" />
          <Property Name="CreatedBy" Type="Int64" />
          <Property Name="CreatedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UpdatedBy" Type="Int64" />
          <Property Name="UpdatedDate" Type="DateTime" Nullable="false" Precision="3" />
        </EntityType>
        <EntityType Name="V32UserGroup">
          <Key>
            <PropertyRef Name="UserGroupId" />
            <PropertyRef Name="UserId" />
            <PropertyRef Name="GroupId" />
          </Key>
          <Property Name="UserGroupId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="UserId" Type="Int32" Nullable="false" />
          <Property Name="GroupId" Type="Int32" Nullable="false" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="CreatedBy" Type="Int64" />
          <Property Name="CreatedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="ModifiedBy" Type="Int64" />
          <Property Name="ModifiedDate" Type="DateTime" Nullable="false" Precision="3" />
        </EntityType>
        <EntityType Name="Category">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CategoryName" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="CategoryImage" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="GeometryImage" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="IsDelete" Type="Boolean" Nullable="false" />
          <Property Name="CreatedBy" Type="Int64" />
          <Property Name="CreatedDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="ModifiedBy" Type="Int16" />
          <Property Name="ModifiedDate" Type="DateTime" Nullable="false" Precision="0" />
        </EntityType>
        <EntityType Name="Device">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="DeviceIMEI" />
            <PropertyRef Name="TypeId" />
          </Key>
          <Property Name="ID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="DeviceIMEI" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ProgrammedOn" Type="DateTime" Precision="3" />
          <Property Name="DeviceType" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Description" Type="String" MaxLength="500" FixedLength="false" Unicode="false" />
          <Property Name="TimeZone" Type="Int32" />
          <Property Name="MCC" Type="Int32" />
          <Property Name="MNC" Type="Int32" />
          <Property Name="SIMNO" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="SIMIMSI" Type="String" MaxLength="30" FixedLength="false" Unicode="false" />
          <Property Name="CountryCode" Type="Int32" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="IsDelete" Type="Boolean" Nullable="false" />
          <Property Name="CreatedBy" Type="Int64" />
          <Property Name="CreatedDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="ModifiedBy" Type="Int64" />
          <Property Name="ModifiedDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="Ign_Port" Type="Int16" Nullable="false" />
          <Property Name="IsTraceDataRequired" Type="Boolean" />
          <Property Name="TypeId" Type="Int32" Nullable="false" />
          <Property Name="BatchNo" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="ExpiryDate" Type="DateTime" Precision="3" />
          <Property Name="AccessKey" Type="Int32" />
        </EntityType>
        <EntityType Name="VehicleGroup">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="VehicleID" />
            <PropertyRef Name="GroupID" />
          </Key>
          <Property Name="ID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="VehicleID" Type="Int64" Nullable="false" />
          <Property Name="GroupID" Type="Int64" Nullable="false" />
          <Property Name="IsDefault" Type="Boolean" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="IsDelete" Type="Boolean" Nullable="false" />
          <Property Name="CreatedBy" Type="Int64" />
          <Property Name="CreatedDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="ModifiedBy" Type="Int64" />
          <Property Name="ModifiedDate" Type="DateTime" Nullable="false" Precision="0" />
        </EntityType>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="VZoneTrackModelStoreContainer" CdmEntityContainer="VZoneTrackEntities">
          <EntitySetMapping Name="Users">
            <EntityTypeMapping TypeName="VZoneTrackModel.User">
              <MappingFragment StoreEntitySet="Users">
                <ScalarProperty Name="SecretKey" ColumnName="SecretKey" />
                <ScalarProperty Name="IsAsateelInfoUpdated" ColumnName="IsAsateelInfoUpdated" />
                <ScalarProperty Name="RememberMeSetOn" ColumnName="RememberMeSetOn" />
                <ScalarProperty Name="IsRememberMeEnabled" ColumnName="IsRememberMeEnabled" />
                <ScalarProperty Name="AuthenticationLogging" ColumnName="AuthenticationLogging" />
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="CompanyID" ColumnName="CompanyID" />
                <ScalarProperty Name="FirstName" ColumnName="FirstName" />
                <ScalarProperty Name="LastName" ColumnName="LastName" />
                <ScalarProperty Name="UserName" ColumnName="UserName" />
                <ScalarProperty Name="Password" ColumnName="Password" />
                <ScalarProperty Name="IsAdmin" ColumnName="IsAdmin" />
                <ScalarProperty Name="LastLoggedIn" ColumnName="LastLoggedIn" />
                <ScalarProperty Name="TotalLogins" ColumnName="TotalLogins" />
                <ScalarProperty Name="IsSystemDefined" ColumnName="IsSystemDefined" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="IsDeleted" ColumnName="IsDeleted" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="IsTrialAccount" ColumnName="IsTrialAccount" />
                <ScalarProperty Name="TrialExpiry" ColumnName="TrialExpiry" />
                <ScalarProperty Name="Alerts_ReadTime" ColumnName="Alerts_ReadTime" />
                <ScalarProperty Name="IsOnline" ColumnName="IsOnline" />
                <ScalarProperty Name="UserImage" ColumnName="UserImage" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="IsForceRebootRequired" ColumnName="IsForceRebootRequired" />
                <ScalarProperty Name="RebootRequestType" ColumnName="RebootRequestType" />
                <ScalarProperty Name="User_Email" ColumnName="User_Email" />
                <ScalarProperty Name="MobileNo" ColumnName="MobileNo" />
                <ScalarProperty Name="Department" ColumnName="Department" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="OfficeLocation" ColumnName="OfficeLocation" />
                <ScalarProperty Name="Remarks" ColumnName="Remarks" />
                <ScalarProperty Name="IsProfileUpdated" ColumnName="IsProfileUpdated" />
                <ScalarProperty Name="ProfileUpdatedOn" ColumnName="ProfileUpdatedOn" />
                <ScalarProperty Name="IsTrainingRequired" ColumnName="IsTrainingRequired" />
                <ScalarProperty Name="TrainingGivenOn" ColumnName="TrainingGivenOn" />
                <ScalarProperty Name="IsMentor" ColumnName="IsMentor" />
                <ScalarProperty Name="IsEmailVerified" ColumnName="IsEmailVerified" />
                <ScalarProperty Name="IsStalledNotificationReqd" ColumnName="IsStalledNotificationReqd" />
                <ScalarProperty Name="HasWebApiAccess" ColumnName="HasWebApiAccess" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="V41ApiToken">
            <EntityTypeMapping TypeName="VZoneTrackModel.V41ApiToken">
              <MappingFragment StoreEntitySet="V41ApiToken">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="RequestType" ColumnName="RequestType" />
                <ScalarProperty Name="Username" ColumnName="Username" />
                <ScalarProperty Name="PasswordHash" ColumnName="PasswordHash" />
                <ScalarProperty Name="PasswordString" ColumnName="PasswordString" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <FunctionImportMapping FunctionImportName="usp_V41GetApiToken" FunctionName="VZoneTrackModel.Store.usp_V41GetApiToken">
            <ResultMapping>
              <ComplexTypeMapping TypeName="VZoneTrackModel.usp_V41GetApiToken_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="RequestType" ColumnName="RequestType" />
                <ScalarProperty Name="Username" ColumnName="Username" />
                <ScalarProperty Name="PasswordHash" ColumnName="PasswordHash" />
                <ScalarProperty Name="PasswordString" ColumnName="PasswordString" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <EntitySetMapping Name="Vehicle">
            <EntityTypeMapping TypeName="VZoneTrackModel.Vehicle">
              <MappingFragment StoreEntitySet="Vehicle">
                <ScalarProperty Name="AnalyticsJson" ColumnName="AnalyticsJson" />
                <ScalarProperty Name="IsAttendanceDetailRequired" ColumnName="IsAttendanceDetailRequired" />
                <ScalarProperty Name="IsHead" ColumnName="IsHead" />
                <ScalarProperty Name="IsTrailer" ColumnName="IsTrailer" />
                <ScalarProperty Name="IsCase_Authenticated" ColumnName="IsCase_Authenticated" />
                <ScalarProperty Name="IsCaseLevelAuthEnabled" ColumnName="IsCaseLevelAuthEnabled" />
                <ScalarProperty Name="IsDefaultDriftingFilterEnabled" ColumnName="IsDefaultDriftingFilterEnabled" />
                <ScalarProperty Name="ConsDriftingDataOmitCount" ColumnName="ConsDriftingDataOmitCount" />
                <ScalarProperty Name="ThresholdSupplyVoltage" ColumnName="ThresholdSupplyVoltage" />
                <ScalarProperty Name="IsEngineIdleEnabled" ColumnName="IsEngineIdleEnabled" />
                <ScalarProperty Name="ResaleValue" ColumnName="ResaleValue" />
                <ScalarProperty Name="EstimatedKM" ColumnName="EstimatedKM" />
                <ScalarProperty Name="EstimatedMonth" ColumnName="EstimatedMonth" />
                <ScalarProperty Name="MSRP" ColumnName="MSRP" />
                <ScalarProperty Name="Color" ColumnName="Color" />
                <ScalarProperty Name="Trim" ColumnName="Trim" />
                <ScalarProperty Name="ManufactureYear" ColumnName="ManufactureYear" />
                <ScalarProperty Name="VIN" ColumnName="VIN" />
                <ScalarProperty Name="Chiller_Remarks" ColumnName="Chiller_Remarks" />
                <ScalarProperty Name="ChillerTime_SetOn" ColumnName="ChillerTime_SetOn" />
                <ScalarProperty Name="ChillerTimeInMins" ColumnName="ChillerTimeInMins" />
                <ScalarProperty Name="Odometer_SetOn" ColumnName="Odometer_SetOn" />
                <ScalarProperty Name="HasTempCardPunch" ColumnName="HasTempCardPunch" />
                <ScalarProperty Name="CurrentDOUTControlStatus" ColumnName="CurrentDOUTControlStatus" />
                <ScalarProperty Name="DOUTControlPort" ColumnName="DOUTControlPort" />
                <ScalarProperty Name="IsDOUTControlRequired" ColumnName="IsDOUTControlRequired" />
                <ScalarProperty Name="JobIdentificationType" ColumnName="JobIdentificationType" />
                <ScalarProperty Name="ChillerInputPort" ColumnName="ChillerInputPort" />
                <ScalarProperty Name="IsChillerStatusEnabled" ColumnName="IsChillerStatusEnabled" />
                <ScalarProperty Name="StalledDueDate" ColumnName="StalledDueDate" />
                <ScalarProperty Name="CurrentStalledStatus" ColumnName="CurrentStalledStatus" />
                <ScalarProperty Name="HasTrackerOdometer" ColumnName="HasTrackerOdometer" />
                <ScalarProperty Name="AllowedHarshBrkCount" ColumnName="AllowedHarshBrkCount" />
                <ScalarProperty Name="AllowedHarshAccCount" ColumnName="AllowedHarshAccCount" />
                <ScalarProperty Name="AllowedOverspeedDurInMin" ColumnName="AllowedOverspeedDurInMin" />
                <ScalarProperty Name="AllowedIdleDurInMin" ColumnName="AllowedIdleDurInMin" />
                <ScalarProperty Name="EngineSize" ColumnName="EngineSize" />
                <ScalarProperty Name="IdleConsumptionPerHr" ColumnName="IdleConsumptionPerHr" />
                <ScalarProperty Name="TravelKMPerLtr" ColumnName="TravelKMPerLtr" />
                <ScalarProperty Name="IsCanBus" ColumnName="IsCanBus" />
                <ScalarProperty Name="IsImmobilization" ColumnName="IsImmobilization" />
                <ScalarProperty Name="IsBuzzerEnabled" ColumnName="IsBuzzerEnabled" />
                <ScalarProperty Name="IsTeZEventDependsCardSwipe" ColumnName="IsTeZEventDependsCardSwipe" />
                <ScalarProperty Name="IsAuthRFIDType" ColumnName="IsAuthRFIDType" />
                <ScalarProperty Name="IsGadgetInstalled" ColumnName="IsGadgetInstalled" />
                <ScalarProperty Name="IsKMCalcFromField" ColumnName="IsKMCalcFromField" />
                <ScalarProperty Name="IsSwipeOnStartRequired" ColumnName="IsSwipeOnStartRequired" />
                <ScalarProperty Name="IsRFIDBasedDriver" ColumnName="IsRFIDBasedDriver" />
                <ScalarProperty Name="IdleDuration" ColumnName="IdleDuration" />
                <ScalarProperty Name="IsStudentPunch" ColumnName="IsStudentPunch" />
                <ScalarProperty Name="IsBarcodeEnabled" ColumnName="IsBarcodeEnabled" />
                <ScalarProperty Name="IsRFIDEnabled" ColumnName="IsRFIDEnabled" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="IsFuelTank" ColumnName="IsFuelTank" />
                <ScalarProperty Name="IsSeatBelt" ColumnName="IsSeatBelt" />
                <ScalarProperty Name="IsTempSensor" ColumnName="IsTempSensor" />
                <ScalarProperty Name="IsDoorSensor" ColumnName="IsDoorSensor" />
                <ScalarProperty Name="IsDelete" ColumnName="IsDelete" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="Odometer" ColumnName="Odometer" />
                <ScalarProperty Name="Mileage" ColumnName="Mileage" />
                <ScalarProperty Name="InstallationDate" ColumnName="InstallationDate" />
                <ScalarProperty Name="Comments" ColumnName="Comments" />
                <ScalarProperty Name="Picture" ColumnName="Picture" />
                <ScalarProperty Name="MaxSpeed" ColumnName="MaxSpeed" />
                <ScalarProperty Name="FuelTankCapacity" ColumnName="FuelTankCapacity" />
                <ScalarProperty Name="FullVoltage" ColumnName="FullVoltage" />
                <ScalarProperty Name="EmptyVoltage" ColumnName="EmptyVoltage" />
                <ScalarProperty Name="FuelType" ColumnName="FuelType" />
                <ScalarProperty Name="Model" ColumnName="Model" />
                <ScalarProperty Name="Make" ColumnName="Make" />
                <ScalarProperty Name="SuffixSeperator" ColumnName="SuffixSeperator" />
                <ScalarProperty Name="PrefixSeperator" ColumnName="PrefixSeperator" />
                <ScalarProperty Name="Suffix" ColumnName="Suffix" />
                <ScalarProperty Name="RegistrationNo" ColumnName="RegistrationNo" />
                <ScalarProperty Name="DisplayText" ColumnName="DisplayText" />
                <ScalarProperty Name="Prefix" ColumnName="Prefix" />
                <ScalarProperty Name="AssetCode" ColumnName="AssetCode" />
                <ScalarProperty Name="CategoryID" ColumnName="CategoryID" />
                <ScalarProperty Name="DriverID" ColumnName="DriverID" />
                <ScalarProperty Name="DeviceID" ColumnName="DeviceID" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <FunctionImportMapping FunctionImportName="usp_V32GetVehicleTimeZone" FunctionName="VZoneTrackModel.Store.usp_V32GetVehicleTimeZone" />
          <EntitySetMapping Name="V41HHTLog_AlainFarms">
            <EntityTypeMapping TypeName="VZoneTrackModel.V41HHTLog_AlainFarms">
              <MappingFragment StoreEntitySet="V41HHTLog_AlainFarms">
                <ScalarProperty Name="CreatedOn" ColumnName="CreatedOn" />
                <ScalarProperty Name="Salesman" ColumnName="Salesman" />
                <ScalarProperty Name="Supervisor" ColumnName="Supervisor" />
                <ScalarProperty Name="Division" ColumnName="Division" />
                <ScalarProperty Name="SubArea" ColumnName="SubArea" />
                <ScalarProperty Name="DepotCode" ColumnName="DepotCode" />
                <ScalarProperty Name="Status" ColumnName="Status" />
                <ScalarProperty Name="TimeStamp" ColumnName="TimeStamp" />
                <ScalarProperty Name="HHTCode" ColumnName="HHTCode" />
                <ScalarProperty Name="RouteNo" ColumnName="RouteNo" />
                <ScalarProperty Name="HHTLogId" ColumnName="HHTLogId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Company">
            <EntityTypeMapping TypeName="VZoneTrackModel.Company">
              <MappingFragment StoreEntitySet="Company">
                <ScalarProperty Name="ApiRateLimit" ColumnName="ApiRateLimit" />
                <ScalarProperty Name="IsCaseLevelAuthFeatureActivated" ColumnName="IsCaseLevelAuthFeatureActivated" />
                <ScalarProperty Name="HHTCodeRange" ColumnName="HHTCodeRange" />
                <ScalarProperty Name="HHTConfigEnabled" ColumnName="HHTConfigEnabled" />
                <ScalarProperty Name="CancellationReason" ColumnName="CancellationReason" />
                <ScalarProperty Name="UtilizedStorageInKB" ColumnName="UtilizedStorageInKB" />
                <ScalarProperty Name="MaxStorageInMB" ColumnName="MaxStorageInMB" />
                <ScalarProperty Name="IsRequireStalledListAlert" ColumnName="IsRequireStalledListAlert" />
                <ScalarProperty Name="HasPassengerTracking" ColumnName="HasPassengerTracking" />
                <ScalarProperty Name="TimeZone" ColumnName="TimeZone" />
                <ScalarProperty Name="CustomMessageExpiryOn" ColumnName="CustomMessageExpiryOn" />
                <ScalarProperty Name="CustomMessage" ColumnName="CustomMessage" />
                <ScalarProperty Name="TrialExpiryOn" ColumnName="TrialExpiryOn" />
                <ScalarProperty Name="IsUnderTrial" ColumnName="IsUnderTrial" />
                <ScalarProperty Name="IsPremiumAccount" ColumnName="IsPremiumAccount" />
                <ScalarProperty Name="IsFleetInfoSystemEnabled" ColumnName="IsFleetInfoSystemEnabled" />
                <ScalarProperty Name="IsTempDashboardEnabled" ColumnName="IsTempDashboardEnabled" />
                <ScalarProperty Name="DefaultZoomLevel" ColumnName="DefaultZoomLevel" />
                <ScalarProperty Name="IsVZone4DEnabled" ColumnName="IsVZone4DEnabled" />
                <ScalarProperty Name="IsDeliveryAppEnabled" ColumnName="IsDeliveryAppEnabled" />
                <ScalarProperty Name="AllowVehicleMultipleGroup" ColumnName="AllowVehicleMultipleGroup" />
                <ScalarProperty Name="DBName" ColumnName="DBName" />
                <ScalarProperty Name="AssCompanyID" ColumnName="AssCompanyID" />
                <ScalarProperty Name="DefaultLong" ColumnName="DefaultLong" />
                <ScalarProperty Name="DefaultLat" ColumnName="DefaultLat" />
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="IsDelete" ColumnName="IsDelete" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="IsControlCentre" ColumnName="IsControlCentre" />
                <ScalarProperty Name="IsTeleCall" ColumnName="IsTeleCall" />
                <ScalarProperty Name="DBBackupTime" ColumnName="DBBackupTime" />
                <ScalarProperty Name="Logo" ColumnName="Logo" />
                <ScalarProperty Name="IsPartner" ColumnName="IsPartner" />
                <ScalarProperty Name="Url" ColumnName="Url" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="MobileNo" ColumnName="MobileNo" />
                <ScalarProperty Name="PhoneNo" ColumnName="PhoneNo" />
                <ScalarProperty Name="Address" ColumnName="Address" />
                <ScalarProperty Name="ContactPerson" ColumnName="ContactPerson" />
                <ScalarProperty Name="CompanyName" ColumnName="CompanyName" />
                <ScalarProperty Name="Code" ColumnName="Code" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Driver">
            <EntityTypeMapping TypeName="VZoneTrackModel.Driver">
              <MappingFragment StoreEntitySet="Driver">
                <ScalarProperty Name="IsCase_VehiclePersonnelAllocationId" ColumnName="IsCase_VehiclePersonnelAllocationId" />
                <ScalarProperty Name="IsCase_Tokenized" ColumnName="IsCase_Tokenized" />
                <ScalarProperty Name="IsCase_Authenticated_RefId" ColumnName="IsCase_Authenticated_RefId" />
                <ScalarProperty Name="IsCase_Authenticated_At" ColumnName="IsCase_Authenticated_At" />
                <ScalarProperty Name="IsCase_Authenticated" ColumnName="IsCase_Authenticated" />
                <ScalarProperty Name="Remarks" ColumnName="Remarks" />
                <ScalarProperty Name="IsSystemDefined" ColumnName="IsSystemDefined" />
                <ScalarProperty Name="IssueDate" ColumnName="IssueDate" />
                <ScalarProperty Name="ExpiryDate" ColumnName="ExpiryDate" />
                <ScalarProperty Name="DOB" ColumnName="DOB" />
                <ScalarProperty Name="Nationality" ColumnName="Nationality" />
                <ScalarProperty Name="IDNumber" ColumnName="IDNumber" />
                <ScalarProperty Name="DriverCardID" ColumnName="DriverCardID" />
                <ScalarProperty Name="TempCardType" ColumnName="TempCardType" />
                <ScalarProperty Name="IsDriver" ColumnName="IsDriver" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="IsDelete" ColumnName="IsDelete" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="Picture" ColumnName="Picture" />
                <ScalarProperty Name="Designation" ColumnName="Designation" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="MobileNo" ColumnName="MobileNo" />
                <ScalarProperty Name="LicenseExpiryDate" ColumnName="LicenseExpiryDate" />
                <ScalarProperty Name="LicenseIssueDate" ColumnName="LicenseIssueDate" />
                <ScalarProperty Name="LicenseNo" ColumnName="LicenseNo" />
                <ScalarProperty Name="DellasKey" ColumnName="DellasKey" />
                <ScalarProperty Name="DriverName" ColumnName="DriverName" />
                <ScalarProperty Name="CompanyID" ColumnName="CompanyID" />
                <ScalarProperty Name="Code" ColumnName="Code" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="V32DriverIDCard">
            <EntityTypeMapping TypeName="VZoneTrackModel.V32DriverIDCard">
              <MappingFragment StoreEntitySet="V32DriverIDCard">
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="Status" ColumnName="Status" />
                <ScalarProperty Name="CardNumber" ColumnName="CardNumber" />
                <ScalarProperty Name="CardType" ColumnName="CardType" />
                <ScalarProperty Name="CompanyID" ColumnName="CompanyID" />
                <ScalarProperty Name="DriverCardID" ColumnName="DriverCardID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <FunctionImportMapping FunctionImportName="usp_V32GetVehiclesInCompany" FunctionName="VZoneTrackModel.Store.usp_V32GetVehiclesInCompany" />
          <FunctionImportMapping FunctionImportName="GeVehiclesInCompany" FunctionName="VZoneTrackModel.Store.usp_V32GetVehiclesInCompany">
            <ResultMapping>
              <ComplexTypeMapping TypeName="VZoneTrackModel.VehiclesInCompany">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="VehicleId" ColumnName="VehicleId" />
                <ScalarProperty Name="AssetCode" ColumnName="AssetCode" />
                <ScalarProperty Name="DisplayText" ColumnName="DisplayText" />
                <ScalarProperty Name="RegistrationNo" ColumnName="RegistrationNo" />
                <ScalarProperty Name="Make" ColumnName="Make" />
                <ScalarProperty Name="Model" ColumnName="Model" />
                <ScalarProperty Name="FuelType" ColumnName="FuelType" />
                <ScalarProperty Name="FuelTankCapacity" ColumnName="FuelTankCapacity" />
                <ScalarProperty Name="AllowedSpeed" ColumnName="AllowedSpeed" />
                <ScalarProperty Name="InstallationDate" ColumnName="InstallationDate" />
                <ScalarProperty Name="InitialOdometer" ColumnName="InitialOdometer" />
                <ScalarProperty Name="InitialOdometerSetDate" ColumnName="InitialOdometerSetDate" />
                <ScalarProperty Name="TempSensorEnabled" ColumnName="TempSensorEnabled" />
                <ScalarProperty Name="DoorSensorEnabled" ColumnName="DoorSensorEnabled" />
                <ScalarProperty Name="CanBusEnabled" ColumnName="CanBusEnabled" />
                <ScalarProperty Name="ChillerEnabled" ColumnName="ChillerEnabled" />
                <ScalarProperty Name="EngineIdleDetectEnabled" ColumnName="EngineIdleDetectEnabled" />
                <ScalarProperty Name="CaseAuthEnabled" ColumnName="CaseAuthEnabled" />
                <ScalarProperty Name="TravelKMPerLitre" ColumnName="TravelKMPerLitre" />
                <ScalarProperty Name="IdleConsPerLitre" ColumnName="IdleConsPerLitre" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="usp_V41GetCompanyVehicles" FunctionName="VZoneTrackModel.Store.usp_V41GetCompanyVehicles" />
          <FunctionImportMapping FunctionImportName="usp_GetVehiclesDetails" FunctionName="VZoneTrackModel.Store.usp_GetVehiclesDetails" />
          <FunctionImportMapping FunctionImportName="GetVehiclesDetails" FunctionName="VZoneTrackModel.Store.usp_GetVehiclesDetails">
            <ResultMapping>
              <ComplexTypeMapping TypeName="VZoneTrackModel.VehiclesInfoComplex">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="VehicleId" ColumnName="VehicleId" />
                <ScalarProperty Name="DeviceId" ColumnName="DeviceId" />
                <ScalarProperty Name="TypeId" ColumnName="TypeId" />
                <ScalarProperty Name="DeviceIMEI" ColumnName="DeviceIMEI" />
                <ScalarProperty Name="SIMNO" ColumnName="SIMNO" />
                <ScalarProperty Name="ManufactureId" ColumnName="ManufactureId" />
                <ScalarProperty Name="AssetCode" ColumnName="AssetCode" />
                <ScalarProperty Name="DisplayText" ColumnName="DisplayText" />
                <ScalarProperty Name="PlateNo" ColumnName="PlateNo" />
                <ScalarProperty Name="Model" ColumnName="Model" />
                <ScalarProperty Name="MaxSpeed" ColumnName="MaxSpeed" />
                <ScalarProperty Name="CategoryId" ColumnName="CategoryId" />
                <ScalarProperty Name="CategoryName" ColumnName="CategoryName" />
                <ScalarProperty Name="CategoryImage" ColumnName="CategoryImage" />
                <ScalarProperty Name="IsTempSensor" ColumnName="IsTempSensor" />
                <ScalarProperty Name="IsRFIDEnabled" ColumnName="IsRFIDEnabled" />
                <ScalarProperty Name="IsDoorSensor" ColumnName="IsDoorSensor" />
                <ScalarProperty Name="IsSeatbelt" ColumnName="IsSeatbelt" />
                <ScalarProperty Name="IsAnyTemperatureEnabled" ColumnName="IsAnyTemperatureEnabled" />
                <ScalarProperty Name="IdleDataInterval" ColumnName="IdleDataInterval" />
                <ScalarProperty Name="TimeZone" ColumnName="TimeZone" />
                <ScalarProperty Name="IsGadgetInstalled" ColumnName="IsGadgetInstalled" />
                <ScalarProperty Name="IsCANBUSEnabled" ColumnName="IsCANBUSEnabled" />
                <ScalarProperty Name="FuelTankCapacity" ColumnName="FuelTankCapacity" />
                <ScalarProperty Name="CurrentStalledStatus" ColumnName="CurrentStalledStatus" />
                <ScalarProperty Name="ChillerInputPort" ColumnName="ChillerInputPort" />
                <ScalarProperty Name="IsChillerStatusEnabled" ColumnName="IsChillerStatusEnabled" />
                <ScalarProperty Name="IsEngineIdleEnabled" ColumnName="IsEngineIdleEnabled" />
                <ScalarProperty Name="ThresholdSupplyVoltage" ColumnName="ThresholdSupplyVoltage" />
                <ScalarProperty Name="ConsDriftingDataOmitCount" ColumnName="ConsDriftingDataOmitCount" />
                <ScalarProperty Name="IsDefaultDriftingFilterEnabled" ColumnName="IsDefaultDriftingFilterEnabled" />
                <ScalarProperty Name="IsDoorSensorEnabled" ColumnName="IsDoorSensorEnabled" />
                <ScalarProperty Name="FuelType" ColumnName="FuelType" />
                <ScalarProperty Name="Color" ColumnName="Color" />
                <ScalarProperty Name="VIN" ColumnName="VIN" />
                <ScalarProperty Name="ManufactureYear" ColumnName="ManufactureYear" />
                <ScalarProperty Name="MSRP" ColumnName="MSRP" />
                <ScalarProperty Name="VehicleTrim" ColumnName="VehicleTrim" />
                <ScalarProperty Name="InstallationDate" ColumnName="InstallationDate" />
                <ScalarProperty Name="IsBuzzerEnabled" ColumnName="IsBuzzerEnabled" />
                <ScalarProperty Name="IsImmobilizationEnabled" ColumnName="IsImmobilizationEnabled" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <EntitySetMapping Name="AnalyticsTripDetail">
            <EntityTypeMapping TypeName="VZoneTrackModel.AnalyticsTripDetail">
              <MappingFragment StoreEntitySet="AnalyticsTripDetail">
                <ScalarProperty Name="ChillerDetailJson" ColumnName="ChillerDetailJson" />
                <ScalarProperty Name="SwipeDetailJson" ColumnName="SwipeDetailJson" />
                <ScalarProperty Name="TemperatureDetailJson" ColumnName="TemperatureDetailJson" />
                <ScalarProperty Name="DoorDetailJson" ColumnName="DoorDetailJson" />
                <ScalarProperty Name="AlertDetailJson" ColumnName="AlertDetailJson" />
                <ScalarProperty Name="DistanceTraveled" ColumnName="DistanceTraveled" />
                <ScalarProperty Name="FuelCost" ColumnName="FuelCost" />
                <ScalarProperty Name="FuelCons" ColumnName="FuelCons" />
                <ScalarProperty Name="TripDurationMin" ColumnName="TripDurationMin" />
                <ScalarProperty Name="IdleDurationMin" ColumnName="IdleDurationMin" />
                <ScalarProperty Name="TravelDurationMin" ColumnName="TravelDurationMin" />
                <ScalarProperty Name="EndTime" ColumnName="EndTime" />
                <ScalarProperty Name="EndLongitude" ColumnName="EndLongitude" />
                <ScalarProperty Name="EndLatitude" ColumnName="EndLatitude" />
                <ScalarProperty Name="EndLocation" ColumnName="EndLocation" />
                <ScalarProperty Name="StartTime" ColumnName="StartTime" />
                <ScalarProperty Name="StartLongitude" ColumnName="StartLongitude" />
                <ScalarProperty Name="StartLatitude" ColumnName="StartLatitude" />
                <ScalarProperty Name="StartLocation" ColumnName="StartLocation" />
                <ScalarProperty Name="DriverId" ColumnName="DriverId" />
                <ScalarProperty Name="VehicleId" ColumnName="VehicleId" />
                <ScalarProperty Name="CompanyId" ColumnName="CompanyId" />
                <ScalarProperty Name="TripSummaryId" ColumnName="TripSummaryId" />
                <ScalarProperty Name="TripDetailId" ColumnName="TripDetailId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="AnalyticsTripSummary">
            <EntityTypeMapping TypeName="VZoneTrackModel.AnalyticsTripSummary">
              <MappingFragment StoreEntitySet="AnalyticsTripSummary">
                <ScalarProperty Name="ChillerSummaryJson" ColumnName="ChillerSummaryJson" />
                <ScalarProperty Name="SwipeSummaryJson" ColumnName="SwipeSummaryJson" />
                <ScalarProperty Name="TemperatureSummaryJson" ColumnName="TemperatureSummaryJson" />
                <ScalarProperty Name="DoorSummaryJson" ColumnName="DoorSummaryJson" />
                <ScalarProperty Name="AlertSummaryJson" ColumnName="AlertSummaryJson" />
                <ScalarProperty Name="DistanceTraveled" ColumnName="DistanceTraveled" />
                <ScalarProperty Name="IdleViolationFuelCost" ColumnName="IdleViolationFuelCost" />
                <ScalarProperty Name="IdleViolationFuelCons" ColumnName="IdleViolationFuelCons" />
                <ScalarProperty Name="IdleViolationDurationMin" ColumnName="IdleViolationDurationMin" />
                <ScalarProperty Name="FuelCost" ColumnName="FuelCost" />
                <ScalarProperty Name="FuelCons" ColumnName="FuelCons" />
                <ScalarProperty Name="TotalDurationMin" ColumnName="TotalDurationMin" />
                <ScalarProperty Name="StopDurationMin" ColumnName="StopDurationMin" />
                <ScalarProperty Name="IdleDurationMin" ColumnName="IdleDurationMin" />
                <ScalarProperty Name="TravelDurationMin" ColumnName="TravelDurationMin" />
                <ScalarProperty Name="AvgSpeed" ColumnName="AvgSpeed" />
                <ScalarProperty Name="TopSpeed" ColumnName="TopSpeed" />
                <ScalarProperty Name="TripDate" ColumnName="TripDate" />
                <ScalarProperty Name="VehicleId" ColumnName="VehicleId" />
                <ScalarProperty Name="CompanyId" ColumnName="CompanyId" />
                <ScalarProperty Name="TripSummaryId" ColumnName="TripSummaryId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="LastData">
            <EntityTypeMapping TypeName="VZoneTrackModel.LastData">
              <MappingFragment StoreEntitySet="LastData">
                <ScalarProperty Name="LastEventProcessedTime" ColumnName="LastEventProcessedTime" />
                <ScalarProperty Name="PropertyList" ColumnName="PropertyList" />
                <ScalarProperty Name="IButtonValue" ColumnName="IButtonValue" />
                <ScalarProperty Name="TempTagList" ColumnName="TempTagList" />
                <ScalarProperty Name="TotStop" ColumnName="TotStop" />
                <ScalarProperty Name="TotIdle" ColumnName="TotIdle" />
                <ScalarProperty Name="TotTravel" ColumnName="TotTravel" />
                <ScalarProperty Name="AwayAt" ColumnName="AwayAt" />
                <ScalarProperty Name="LandmarkName" ColumnName="LandmarkName" />
                <ScalarProperty Name="LandmarkID" ColumnName="LandmarkID" />
                <ScalarProperty Name="FirstStartTime" ColumnName="FirstStartTime" />
                <ScalarProperty Name="HighSpeed" ColumnName="HighSpeed" />
                <ScalarProperty Name="IsLandmark" ColumnName="IsLandmark" />
                <ScalarProperty Name="RFID" ColumnName="RFID" />
                <ScalarProperty Name="WE" ColumnName="WE" />
                <ScalarProperty Name="NS" ColumnName="NS" />
                <ScalarProperty Name="GPRMCState" ColumnName="GPRMCState" />
                <ScalarProperty Name="Alarm" ColumnName="Alarm" />
                <ScalarProperty Name="FuelTank" ColumnName="FuelTank" />
                <ScalarProperty Name="LACCIL" ColumnName="LACCIL" />
                <ScalarProperty Name="LACCIC" ColumnName="LACCIC" />
                <ScalarProperty Name="CompanyID" ColumnName="CompanyID" />
                <ScalarProperty Name="VDOP" ColumnName="VDOP" />
                <ScalarProperty Name="HDOP" ColumnName="HDOP" />
                <ScalarProperty Name="PDOP" ColumnName="PDOP" />
                <ScalarProperty Name="SeatBelt" ColumnName="SeatBelt" />
                <ScalarProperty Name="TrackEvent" ColumnName="TrackEvent" />
                <ScalarProperty Name="DoorSensor" ColumnName="DoorSensor" />
                <ScalarProperty Name="LocationName" ColumnName="LocationName" />
                <ScalarProperty Name="Job" ColumnName="Job" />
                <ScalarProperty Name="CurrentOdometer" ColumnName="CurrentOdometer" />
                <ScalarProperty Name="TotalMilemeter" ColumnName="TotalMilemeter" />
                <ScalarProperty Name="Temperature" ColumnName="Temperature" />
                <ScalarProperty Name="FuelSensor" ColumnName="FuelSensor" />
                <ScalarProperty Name="ADC1Collect" ColumnName="ADC1Collect" />
                <ScalarProperty Name="ADC0Collect" ColumnName="ADC0Collect" />
                <ScalarProperty Name="ChargeVoltage" ColumnName="ChargeVoltage" />
                <ScalarProperty Name="BatteryVoltage" ColumnName="BatteryVoltage" />
                <ScalarProperty Name="ChargeStatus" ColumnName="ChargeStatus" />
                <ScalarProperty Name="Status" ColumnName="Status" />
                <ScalarProperty Name="ServerTime" ColumnName="ServerTime" />
                <ScalarProperty Name="TrackTime" ColumnName="TrackTime" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="SIMNo" ColumnName="SIMNo" />
                <ScalarProperty Name="Longitude" ColumnName="Longitude" />
                <ScalarProperty Name="Latitude" ColumnName="Latitude" />
                <ScalarProperty Name="DriverID" ColumnName="DriverID" />
                <ScalarProperty Name="MovingDirection" ColumnName="MovingDirection" />
                <ScalarProperty Name="Speed" ColumnName="Speed" />
                <ScalarProperty Name="TraceCode" ColumnName="TraceCode" />
                <ScalarProperty Name="TrackingID" ColumnName="TrackingID" />
                <ScalarProperty Name="VehicleID" ColumnName="VehicleID" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Group">
            <EntityTypeMapping TypeName="VZoneTrackModel.Group">
              <MappingFragment StoreEntitySet="Group">
                <ScalarProperty Name="UpdatedDate" ColumnName="UpdatedDate" />
                <ScalarProperty Name="UpdatedBy" ColumnName="UpdatedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="IsDelete" ColumnName="IsDelete" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="IsSystemDefined" ColumnName="IsSystemDefined" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="GroupName" ColumnName="GroupName" />
                <ScalarProperty Name="CompanyID" ColumnName="CompanyID" />
                <ScalarProperty Name="Code" ColumnName="Code" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="V32UserGroup">
            <EntityTypeMapping TypeName="VZoneTrackModel.V32UserGroup">
              <MappingFragment StoreEntitySet="V32UserGroup">
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="GroupId" ColumnName="GroupId" />
                <ScalarProperty Name="UserId" ColumnName="UserId" />
                <ScalarProperty Name="UserGroupId" ColumnName="UserGroupId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Category">
            <EntityTypeMapping TypeName="VZoneTrackModel.Category">
              <MappingFragment StoreEntitySet="Category">
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="IsDelete" ColumnName="IsDelete" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="GeometryImage" ColumnName="GeometryImage" />
                <ScalarProperty Name="CategoryImage" ColumnName="CategoryImage" />
                <ScalarProperty Name="CategoryName" ColumnName="CategoryName" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Device">
            <EntityTypeMapping TypeName="VZoneTrackModel.Device">
              <MappingFragment StoreEntitySet="Device">
                <ScalarProperty Name="AccessKey" ColumnName="AccessKey" />
                <ScalarProperty Name="ExpiryDate" ColumnName="ExpiryDate" />
                <ScalarProperty Name="BatchNo" ColumnName="BatchNo" />
                <ScalarProperty Name="TypeId" ColumnName="TypeId" />
                <ScalarProperty Name="IsTraceDataRequired" ColumnName="IsTraceDataRequired" />
                <ScalarProperty Name="Ign_Port" ColumnName="Ign_Port" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="IsDelete" ColumnName="IsDelete" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="CountryCode" ColumnName="CountryCode" />
                <ScalarProperty Name="SIMIMSI" ColumnName="SIMIMSI" />
                <ScalarProperty Name="SIMNO" ColumnName="SIMNO" />
                <ScalarProperty Name="MNC" ColumnName="MNC" />
                <ScalarProperty Name="MCC" ColumnName="MCC" />
                <ScalarProperty Name="TimeZone" ColumnName="TimeZone" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="DeviceType" ColumnName="DeviceType" />
                <ScalarProperty Name="ProgrammedOn" ColumnName="ProgrammedOn" />
                <ScalarProperty Name="DeviceIMEI" ColumnName="DeviceIMEI" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="VehicleGroup">
            <EntityTypeMapping TypeName="VZoneTrackModel.VehicleGroup">
              <MappingFragment StoreEntitySet="VehicleGroup">
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="IsDelete" ColumnName="IsDelete" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="IsDefault" ColumnName="IsDefault" />
                <ScalarProperty Name="GroupID" ColumnName="GroupID" />
                <ScalarProperty Name="VehicleID" ColumnName="VehicleID" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="False" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="False" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>