﻿using System.Net;
using System.Net.Http;
using System.Web.Http.Controllers;
using System.Web.Http.Filters;
using VZoneTrack.Gps.V32.WebApi.Controllers;
using VZoneTrack.Gps.V32.WebApi.Interfaces;

namespace VZoneTrack.Gps.V32.WebApi.Code
{
    public class AuthorizeAndRateLimitAttribute : ActionFilterAttribute
    {
        private static readonly RateLimiter rateLimiter = RateLimiter.Instance;

        public override void OnActionExecuting(HttpActionContext actionContext)
        {
            var controller = actionContext.ControllerContext.Controller as IVZoneControllerContext;
            if (controller == null)
            {
                base.OnActionExecuting(actionContext);
                return;
            }

            string userName = controller.GetUserDetails();
            string apiType = controller.GetApiType();
            string requestType = controller.GetRequestType();
            bool is2FAVerified = controller.TwoFactorAuthenticateUser(userName, apiType);
            long companyId = EntityProvider.GetCompanyIdFromUsername(userName);
            int userId = EntityProvider.GetUserIdFromUsername(userName);
            
            if (!rateLimiter.TryAcquire(userName))
            {
                actionContext.Response = actionContext.Request.CreateResponse(HttpStatusCode.Forbidden, new
                {
                    message = "Rate limit exceeded"
                });
                return;
            }

            if (!(apiType == "external" && requestType == "other" && userName != null && is2FAVerified))
            {
                actionContext.Response = actionContext.Request.CreateResponse(HttpStatusCode.Unauthorized, new
                {
                    message = "Unauthorized User!"
                });
                return;
            }

            actionContext.Request.Properties["CompanyId"] = companyId;
            actionContext.Request.Properties["UserId"] = userId;

            base.OnActionExecuting(actionContext);
        }
    }
}