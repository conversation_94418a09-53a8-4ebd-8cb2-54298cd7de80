﻿using log4net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Web;

namespace VZoneTrack.Gps.V32.WebApi.Helpers
{
    public static class FileHelper
    {
        private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// Saves KML content to a file at the specified path.
        /// </summary>
        /// <param name="kmlContent">The KML content as string.</param>
        /// <param name="filePath">The physical file path to save the KML.</param>
        public static void SaveKmlToFile(string kmlContent, string filePath)
        {
            try
            {   
                string directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Write the KML content to file
                File.WriteAllText(filePath, kmlContent, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                log.Error("FileHelper.SaveKmlToFile: " + ex);
                throw;
            }
        }
    }

}