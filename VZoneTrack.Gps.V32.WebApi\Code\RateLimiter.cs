﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;

namespace VZoneTrack.Gps.V32.WebApi.Code
{
    public class RateLimiter
    {        
        private static readonly Dictionary<string, TokenBucket> userBuckets = new Dictionary<string, TokenBucket>();
        private readonly object lockObject = new object();

        public static readonly RateLimiter Instance = new RateLimiter();

        public bool TryAcquire(string username)
        {
            int _rateLimit = EntityProvider.GetApiRateLimit(username);

            if (_rateLimit <= 0)
                return true;

            lock (lockObject)
            {
                if (!userBuckets.TryGetValue(username, out var bucket))
                {
                    bucket = new TokenBucket(1, TimeSpan.FromSeconds(_rateLimit));
                    userBuckets[username] = bucket;
                }
                return bucket.TryAcquire();
            }
        }
    }

    public class TokenBucket
    {
        private readonly int capacity;
        private int tokens;
        private readonly TimeSpan refillTime;
        private readonly object lockObject = new object();
        private DateTime lastRefillTime;

        public TokenBucket(int capacity, TimeSpan refillTime)
        {
            this.capacity = capacity;
            this.tokens = capacity;
            this.refillTime = refillTime;
            this.lastRefillTime = DateTime.Now;
        }

        public bool TryAcquire()
        {
            RefillTokens();

            lock (lockObject)
            {
                if (tokens > 0)
                {
                    tokens--;
                    return true;
                }
                else
                    return false;
            }
        }

        private void RefillTokens()
        {
            lock (lockObject)
            {
                var now = DateTime.Now;
                var elapsed = now - lastRefillTime;

                if (elapsed >= refillTime)
                {
                    var tokensToAdd = (int)(elapsed.TotalSeconds / refillTime.TotalSeconds);
                    tokens = Math.Min(capacity, tokens + tokensToAdd);
                    lastRefillTime = now;
                }                
            }
        }
    }    
}