﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using VZoneTrack.Gps.V32.WebApi.Models;

namespace VZoneTrack.Gps.V32.WebApi.Extensions
{
    public static class TripSummaryExtensions
    {
        public static object MaxByValue(this List<TripSummaryDto> list, string columnName)
        {
            switch (columnName)
            {
                case "TopSpeed":
                    return list.Max(s => (double?)s.TopSpeed);
                case "AvgSpeed":
                    return list.Max(s => (double?)s.AvgSpeed);
                case "FuelCons":
                    return list.Max(s => (double?)s.FuelCons);
                case "DistanceTraveled":
                    return list.Max(s => (double?)s.DistanceTraveled);
                case "TravelDurationMin":
                    return list.Max(s => (double?)s.TravelDurationMin);
                default:
                    return null;
            }
        }

        public static object MinByValue(this List<TripSummaryDto> list, string columnName)
        {
            switch (columnName)
            {
                case "TopSpeed":
                    return list.Min(s => (double?)s.TopSpeed);
                case "AvgSpeed":
                    return list.Min(s => (double?)s.AvgSpeed);
                case "FuelCons":
                    return list.Min(s => (double?)s.FuelCons);
                case "DistanceTraveled":
                    return list.Min(s => (double?)s.DistanceTraveled);
                case "TravelDurationMin":
                    return list.Min(s => (double?)s.TravelDurationMin);
                default:
                    return null;
            }
        }

        public static IEnumerable<object> SelectValue(this IEnumerable<TripSummaryDto> list, string columnName)
        {
            switch (columnName)
            {
                case "TopSpeed":
                    return list.Select(s => (object)s.TopSpeed);
                case "AvgSpeed":
                    return list.Select(s => (object)s.AvgSpeed);
                case "FuelCons":
                    return list.Select(s => (object)s.FuelCons);
                case "DistanceTraveled":
                    return list.Select(s => (object)s.DistanceTraveled);
                case "TravelDurationMin":
                    return list.Select(s => (object)s.TravelDurationMin);
                default:
                    return Enumerable.Empty<object>();
            }
        }
    }
}