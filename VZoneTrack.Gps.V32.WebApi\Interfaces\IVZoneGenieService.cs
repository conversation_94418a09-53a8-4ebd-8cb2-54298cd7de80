﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VZoneTrack.Gps.V32.WebApi.Models;

namespace VZoneTrack.Gps.V32.WebApi.Interfaces
{
    public interface IVZoneGenieService
    {
        Task<T> GetRawDataAsync<T>(string columnName, DateTime? from, DateTime? to, int? vehicleId, long companyId);

        Task<T> GetAggregateDataAsync<T>(string aggregate, string columnName, DateTime? from, DateTime? to, int? vehicleId, long companyId);

        Task<Dictionary<string, object>> GetLookupDataAsync(List<LookupOperation> lookups, DateTime? from, DateTime? to, List<int> vehicleIds, long companyId);

        Task<Dictionary<string, object>> GetLiveDataAsync(List<LookupOperation> lookups, int? vehicleId, long companyId);

        Task<Dictionary<string, object>> GenerateVehicleRouteKmlAsync(DateTime? from, DateTime? to, int? vehicleId, long companyId, int userId);

        // New methods for Detail requests with TopN support
        Task<List<T>> GetDetailRawDataAsync<T>(string columnName, DateTime? from, DateTime? to, int? vehicleId, long companyId, int? topN);

        Task<List<T>> GetDetailAggregateDataAsync<T>(string aggregate, string columnName, DateTime? from, DateTime? to, int? vehicleId, long companyId, int? topN);

        Task<List<Dictionary<string, object>>> GetDetailComputeDataAsync(OperationRequest operation, int? vehicleId, long companyId, DateTime? from, DateTime? to, int? topN);

        Task<List<Dictionary<string, object>>> GetDetailLookupDataAsync(List<LookupOperation> lookups, DateTime? from, DateTime? to, List<int> vehicleIds, long companyId);
    }
}
