﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using VZoneTrack.Gps.V32.WebApi.Interfaces;
using VZoneTrack.Gps.V32.WebApi.Models;
using VZoneTrack.Gps.V32.Business;
using System.Data;
using System.Reflection;
using System.Globalization;
using VZoneTrack.Gps.V32.WebApi.Code;


namespace VZoneTrack.Gps.V32.WebApi.Controllers
{
    [Authorize]
    public class AuthenticationController : ControllerBase
    {
        [HttpGet]
        [Route("Authentication/Allocation/{AllocationDate}/{AssetCode?}")]
        public IHttpActionResult GetVehicleAllocationInfo([FromUri] string AllocationDate, [FromUri] string AssetCode = "") //DateTime Format {yyyyMMdd}
        {
            string userName = GetUserDetails();
            string apiType = GetApiType();
            string requestType = GetRequestType();
            bool _is2FAVerified = TwoFactorAuthenticateUser(userName, apiType);
            VehicleAllocation result = null;

            if (apiType == "external" && requestType == "other" && userName != null && _is2FAVerified)
            {                
                long _companyId = EntityProvider.GetCompanyIdFromUsername(userName);
                long _vehicleId = string.IsNullOrEmpty(AssetCode) ? 0 : EntityProvider.GetVehicleIdFromAssetCode(AssetCode);
                List<long> lstVehicles = new List<long>();

                if (_vehicleId.Equals(0))
                {
                    using (VZoneTrackEntities context = new VZoneTrackEntities())
                    {
                        List<VehiclesInCompany> lstVehiclesInCompany = context.GeVehiclesInCompany(Convert.ToInt32(_companyId), string.Empty).ToList();
                        lstVehicles = lstVehiclesInCompany.AsEnumerable().Where(x=>x.CaseAuthEnabled == true).Select(x => (long)x.VehicleId).ToList();                        
                    }
                }
                else
                {
                    if (!_vehicleId.Equals(0))
                        lstVehicles.Add(_vehicleId);
                }

                DateTime.TryParseExact(AllocationDate, "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime _requestDateTime);

                if (!_requestDateTime.Equals(DateTime.MinValue))
                {
                    AuthenticationMiddleware authenticationMiddleware = new AuthenticationMiddleware();
                    result = authenticationMiddleware.GetVehicleAllocationInfo(Convert.ToInt32(_companyId), _requestDateTime, lstVehicles);
                    return Ok(new
                    {
                        message = "SUCCESS",
                        data = result
                    });
                }
                else
                {
                    return BadRequest("The date provided is either incorrect or in an invalid format");
                }

            }
            return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.Unauthorized, "Unauthorized User!"));
        }
    }
}