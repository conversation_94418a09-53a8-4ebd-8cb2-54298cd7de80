﻿using System;
using System.Collections.Generic;
using System.Globalization;

namespace VZoneTrack.Gps.V32.WebApi.Helpers
{
    public static class TypeHelper
    {
        private static readonly Dictionary<string, Type> TypeMappings = new Dictionary<string, Type>(StringComparer.OrdinalIgnoreCase)
        {
            { "int", typeof(int) },
            { "integer", typeof(int) },
            { "decimal", typeof(decimal) },
            { "double", typeof(double) },
            { "float", typeof(double) },
            { "string", typeof(string) },
            { "datetime", typeof(DateTime) },
            { "bool", typeof(bool) },
            { "boolean", typeof(bool) }
        };

        public static Type GetTypeFromName(string typeName)
        {
            if (string.IsNullOrWhiteSpace(typeName))
                return typeof(object);

            typeName = typeName.Trim();

            if (TypeMappings.TryGetValue(typeName, out var result))
                return result;

            result = Type.GetType(typeName);
            return result ?? typeof(object);
        }

        public static bool IsValidTypeName(string typeName)
        {
            if (string.IsNullOrWhiteSpace(typeName))
                return false;

            typeName = typeName.Trim();

            if (TypeMappings.ContainsKey(typeName))
                return true;

            return Type.GetType(typeName) != null;
        }

        public static object ConvertToType(object value, string dataType)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var targetType = GetTypeFromName(dataType);

            try
            {
                // if value is already of target type, just return
                if (value.GetType() == targetType)
                    return value;

                // handle nullable conversions safely
                if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
                    targetType = Nullable.GetUnderlyingType(targetType);

                return Convert.ChangeType(value, targetType, CultureInfo.InvariantCulture);
            }
            catch (Exception ex)
            {
                // You can log this exception if you have a logger
                // Example: logger.Warn($"Failed to convert value '{value}' to type {targetType}.", ex);

                // fallback: if value type, return default value; if ref type, return null
                return targetType.IsValueType ? Activator.CreateInstance(targetType) : null;
            }
        }
    }
}
