﻿using System;
using System.Collections.Generic;
using System.Linq;
using Unity;
using Unity.Lifetime;
using VZoneTrack.Gps.V32.WebApi.Dependency;
using VZoneTrack.Gps.V32.WebApi.Interfaces;
using VZoneTrack.Gps.V32.WebApi.Services;
using Microsoft.Owin.Security.OAuth;
using Microsoft.Owin.Host.SystemWeb;
using System.Web.Http;

namespace VZoneTrack.Gps.V32.WebApi
{
    public static class WebApiConfig
    {
        public static void Register(HttpConfiguration config)
        {
            // Web API configuration and services
            //config.SuppressDefaultHostAuthentication();
            //config.Filters.Add(new HostAuthenticationFilter(OAuthDefaults.AuthenticationType));

            var container = new UnityContainer();
            container.RegisterType<ITokenService, TokenService>();
            config.DependencyResolver = new UnityResolver(container);

            config.MessageHandlers.Add(new TokenValidationHandler());

            // Web API routes
            config.MapHttpAttributeRoutes();

            config.Routes.MapHttpRoute(
                name: "Default<PERSON><PERSON>",
                routeTemplate: "VZoneTrackAPI/{controller}/{action}/{id}",
                defaults: new { id = RouteParameter.Optional }
            );
        }
    }
}
