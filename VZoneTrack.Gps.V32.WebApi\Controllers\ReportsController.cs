﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Data;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Http.Description;
using VZoneTrack.Gps.V32.WebApi.Code;

namespace VZoneTrack.Gps.V32.WebApi.Controllers
{
    [Authorize]
    public class ReportsController : ControllerBase
    {
        [ApiExplorerSettings(IgnoreApi = true)]
        [HttpPost]        
        public IHttpActionResult GetReport([FromBody] JToken RequestParams, [FromUri] int ReportId)
        {
            string userName = GetUserDetails();
            string apiType = GetApiType();
            string requestType = GetRequestType();
            bool _is2FAVerified = TwoFactorAuthenticateUser(userName, apiType);

            if (apiType == "internal" && requestType == "AutoReport" && userName == "vzonereport" && _is2FAVerified)
            {
                ReportProcessingMiddleware middleWare = new ReportProcessingMiddleware(ReportId);
                DataSet result = middleWare.processReport(RequestParams);
                return Ok(new
                {
                    message = "REPORT_SUCCESS",
                    data = result
                });
            }
            return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.Unauthorized, "Unauthorized User!"));
        }
    }
}