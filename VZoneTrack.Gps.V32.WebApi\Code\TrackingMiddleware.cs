﻿using log4net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using VZoneTrack.Gps.V32.Business;
using VZoneTrack.Gps.V32.WebApi.Helpers;
using VZoneTrack.Gps.V32.WebApi.Models;


namespace VZoneTrack.Gps.V32.WebApi.Code
{
    public class TrackingMiddleware
    {       
        private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        public Itinerary ProcessTrips(Vehicle vehicle, int companyId, DateTime startDate, DateTime endDate)
        {
            Itinerary itinerary = new Itinerary();
            try
            {   
                TrackingBusiness trackingBusiness = new TrackingBusiness();
                DataSet dsResult = trackingBusiness.GetTripDetails(companyId, (int)vehicle.ID, startDate, endDate, true, 1, true, true, true);
                DataTable dtResult = dsResult.Tables[0];
                DataTable dtSwipes = dsResult.Tables[3];
                itinerary.AssetCode = vehicle.AssetCode;
                itinerary.Vehicle = vehicle.DisplayText;

                List<Trip> lstTrips = new List<Trip>();
                foreach (DataRow drwResult in dtResult.Rows)
                {
                    Location startLocation = new Location
                    {
                        Latitude = Convert.ToDouble(drwResult["StartLat"]),
                        Longitude = Convert.ToDouble(drwResult["StartLng"]),
                        Name = drwResult["StartLocation"].ToString()
                    };

                    Location endLocation = new Location
                    {
                        Latitude = Convert.ToDouble(drwResult["EndLat"]),
                        Longitude = Convert.ToDouble(drwResult["EndLng"]),
                        Name = drwResult["EndLocation"].ToString()
                    };

                    int _travelDurInMin = 0;
                    TimeSpan _tsTravelDuration = TimeSpan.FromSeconds(Convert.ToDouble(drwResult["TravelDurSec"]));
                    _travelDurInMin = Convert.ToInt32(_tsTravelDuration.TotalMinutes);

                    int _idleDurInMin = 0;
                    TimeSpan _tsIdleDuration = TimeSpan.FromSeconds(Convert.ToDouble(drwResult["IdleDurSec"]));
                    _idleDurInMin = Convert.ToInt32(_tsIdleDuration.TotalMinutes);

                    Personnel personnel = EntityProvider.GetPersonnelInfo(Convert.ToInt64(drwResult["DriverId"]));


                    Trip trip = new Trip
                    {
                        Operator = personnel,                        
                        StartTime = Convert.ToDateTime(drwResult["StartTime"]),
                        EndTime = Convert.ToDateTime(drwResult["EndTime"]),
                        StartLocation = startLocation,
                        EndLocation = endLocation,
                        Distance = Convert.ToDouble(drwResult["TripDistance"]),
                        TravelDurationInMin = _travelDurInMin,
                        IdleDurationInMin = _idleDurInMin,
                        FuelConsLtr = Convert.ToDouble(drwResult["FuelConsumed"]),
                        FuelCost = Convert.ToDouble(drwResult["FuelCost"]),
                        TopSpeed = Convert.ToInt32(drwResult["MaxSpeed"]),
                        AvgSpeed = Convert.ToInt32(drwResult["AvgSpeed"])
                    };

                    lstTrips.Add(trip);
                }
                itinerary.Trips = lstTrips;

                List<Swipe> lstSwipes = new List<Swipe>();
                foreach (DataRow drwSwipe in dtSwipes.Rows)
                {
                    Swipe swipe = new Swipe();

                    Location location = new Location
                    {
                        Latitude = Convert.ToDouble(drwSwipe["Latitude"]),
                        Longitude = Convert.ToDouble(drwSwipe["Longitude"]),
                        Name = drwSwipe["Location"].ToString()
                    };

                    swipe.Personnel = EntityProvider.GetPersonnelInfo(drwSwipe["CardNumber"].ToString());
                    swipe.SwipedAt = Convert.ToDateTime(drwSwipe["SwipeAt"]);
                    swipe.Location = location;

                    lstSwipes.Add(swipe);
                }
                itinerary.SwipeInfo = lstSwipes;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
            }

            return itinerary;
        }

        public async Task<string> GenerateVehicleRouteKmlAsync(long companyId, int userId, int vehicleId, DateTime date)
        {
            string kmlUrl = string.Empty;
            try
            {
                DashboardBusiness _dashboardBusiness = new DashboardBusiness();
                DataSet dsResult = _dashboardBusiness.GetQuickRouteDateBased(companyId, userId, vehicleId, date);
                DataTable dtResult = dsResult.Tables[0];
                List<RouteInfo> routes = new List<RouteInfo>();

                foreach (DataRow drwResult in dtResult.Rows)
                {
                    RouteInfo route = new RouteInfo
                    {
                        TrackingId = Convert.ToInt64(drwResult["TrackingId"]),
                        TrackTime = Convert.ToDateTime(drwResult["TrackTime"]),
                        Latitude = Convert.ToDouble(drwResult["Latitude"]),
                        Longitude = Convert.ToDouble(drwResult["Longitude"]),
                        Ignition = drwResult["Ignition"].ToString(),
                        Direction = Convert.ToDouble(drwResult["Direction"]),
                    };
                    routes.Add(route);
                }

                IEnumerable<(double Latitude, double Longitude)> routePoints = routes
                    .OrderBy(r => r.TrackTime)
                    .Select(r => (r.Latitude, r.Longitude));

                string plateNo = dtResult.Rows[0]["PlateNo"].ToString();
                plateNo = plateNo.Replace(" ", "");
                plateNo = Regex.Replace(plateNo, @"\s+", "");
                plateNo = Regex.Replace(plateNo, @"[^a-zA-Z0-9]", "_");

                string randomCode = Guid.NewGuid().ToString("N").Substring(0, 6);
                string name = name = plateNo + "_" + date.ToString("ddMMyyyy") + "_" + randomCode;

                string description = $"The route for vehicle {plateNo} on {date:dd-MMM-yyyy}";

                string kmlContent = KmlGenerator.CreateKmlFile(routes, name, description);

                string folderPath = ConfigurationManager.AppSettings["KML_FILE_PHYSICAL_PATH"];
                string baseUrl = ConfigurationManager.AppSettings["KML_FILE_BASE_URL"];

                if (string.IsNullOrEmpty(folderPath) || string.IsNullOrEmpty(baseUrl))
                {
                    log.Error("KML archive path or base URL is not configured in web.config");
                    throw new Exception("KML archive path or base URL is not configured in web.config");
                }

                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }

                string fileName = name + ".kml";
                string filePath = Path.Combine(folderPath, fileName);
                string webUrl = baseUrl + fileName;

                using (var writer = new StreamWriter(filePath, false))
                {
                    await writer.WriteAsync(kmlContent);
                }

                kmlUrl = webUrl;                
            }
            catch (Exception ex)
            {
                log.Error($"Error in GenerateVehicleRouteKmlAsync: {ex.Message}", ex);
            }

            return kmlUrl;
        }

    }
}