﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace VZoneTrack.Gps.V32.WebApi
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    using System.Data.Entity.Core.Objects;
    using System.Linq;
    
    public partial class VZoneTrackEntities : DbContext
    {
        public VZoneTrackEntities()
            : base("name=VZoneTrackEntities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<User> Users { get; set; }
        public virtual DbSet<V41ApiToken> V41ApiToken { get; set; }
        public virtual DbSet<Vehicle> Vehicle { get; set; }
        public virtual DbSet<V41HHTLog_AlainFarms> V41HHTLog_AlainFarms { get; set; }
        public virtual DbSet<Company> Company { get; set; }
        public virtual DbSet<Driver> Driver { get; set; }
        public virtual DbSet<V32DriverIDCard> V32DriverIDCard { get; set; }
        public virtual DbSet<AnalyticsTripDetail> AnalyticsTripDetail { get; set; }
        public virtual DbSet<AnalyticsTripSummary> AnalyticsTripSummary { get; set; }
        public virtual DbSet<LastData> LastData { get; set; }
        public virtual DbSet<Group> Group { get; set; }
        public virtual DbSet<V32UserGroup> V32UserGroup { get; set; }
        public virtual DbSet<Category> Category { get; set; }
        public virtual DbSet<Device> Device { get; set; }
        public virtual DbSet<VehicleGroup> VehicleGroup { get; set; }
    
        public virtual ObjectResult<usp_V41GetApiToken_Result> usp_V41GetApiToken(string in_Username)
        {
            var in_UsernameParameter = in_Username != null ?
                new ObjectParameter("in_Username", in_Username) :
                new ObjectParameter("in_Username", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_V41GetApiToken_Result>("usp_V41GetApiToken", in_UsernameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> usp_V32GetVehicleTimeZone(Nullable<int> in_VehicleId)
        {
            var in_VehicleIdParameter = in_VehicleId.HasValue ?
                new ObjectParameter("in_VehicleId", in_VehicleId) :
                new ObjectParameter("in_VehicleId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("usp_V32GetVehicleTimeZone", in_VehicleIdParameter);
        }
    
        public virtual int usp_V32GetVehiclesInCompany(Nullable<int> iN_CompanyId, string iN_FilterJson)
        {
            var iN_CompanyIdParameter = iN_CompanyId.HasValue ?
                new ObjectParameter("IN_CompanyId", iN_CompanyId) :
                new ObjectParameter("IN_CompanyId", typeof(int));
    
            var iN_FilterJsonParameter = iN_FilterJson != null ?
                new ObjectParameter("IN_FilterJson", iN_FilterJson) :
                new ObjectParameter("IN_FilterJson", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_V32GetVehiclesInCompany", iN_CompanyIdParameter, iN_FilterJsonParameter);
        }
    
        public virtual ObjectResult<VehiclesInCompany> GeVehiclesInCompany(Nullable<int> iN_CompanyId, string iN_FilterJson)
        {
            var iN_CompanyIdParameter = iN_CompanyId.HasValue ?
                new ObjectParameter("IN_CompanyId", iN_CompanyId) :
                new ObjectParameter("IN_CompanyId", typeof(int));
    
            var iN_FilterJsonParameter = iN_FilterJson != null ?
                new ObjectParameter("IN_FilterJson", iN_FilterJson) :
                new ObjectParameter("IN_FilterJson", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<VehiclesInCompany>("GeVehiclesInCompany", iN_CompanyIdParameter, iN_FilterJsonParameter);
        }
    
        public virtual int usp_V41GetCompanyVehicles(Nullable<long> in_CompanyId, Nullable<int> in_UserId, string in_strGroups)
        {
            var in_CompanyIdParameter = in_CompanyId.HasValue ?
                new ObjectParameter("in_CompanyId", in_CompanyId) :
                new ObjectParameter("in_CompanyId", typeof(long));
    
            var in_UserIdParameter = in_UserId.HasValue ?
                new ObjectParameter("in_UserId", in_UserId) :
                new ObjectParameter("in_UserId", typeof(int));
    
            var in_strGroupsParameter = in_strGroups != null ?
                new ObjectParameter("in_strGroups", in_strGroups) :
                new ObjectParameter("in_strGroups", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_V41GetCompanyVehicles", in_CompanyIdParameter, in_UserIdParameter, in_strGroupsParameter);
        }
    
        public virtual int usp_GetVehiclesDetails(Nullable<long> in_CompanyId, string in_AssetCode)
        {
            var in_CompanyIdParameter = in_CompanyId.HasValue ?
                new ObjectParameter("in_CompanyId", in_CompanyId) :
                new ObjectParameter("in_CompanyId", typeof(long));
    
            var in_AssetCodeParameter = in_AssetCode != null ?
                new ObjectParameter("in_AssetCode", in_AssetCode) :
                new ObjectParameter("in_AssetCode", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("usp_GetVehiclesDetails", in_CompanyIdParameter, in_AssetCodeParameter);
        }
    
        public virtual ObjectResult<VehiclesInfoComplex> GetVehiclesDetails(Nullable<long> in_CompanyId, string in_AssetCode)
        {
            var in_CompanyIdParameter = in_CompanyId.HasValue ?
                new ObjectParameter("in_CompanyId", in_CompanyId) :
                new ObjectParameter("in_CompanyId", typeof(long));
    
            var in_AssetCodeParameter = in_AssetCode != null ?
                new ObjectParameter("in_AssetCode", in_AssetCode) :
                new ObjectParameter("in_AssetCode", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<VehiclesInfoComplex>("GetVehiclesDetails", in_CompanyIdParameter, in_AssetCodeParameter);
        }
    }
}
