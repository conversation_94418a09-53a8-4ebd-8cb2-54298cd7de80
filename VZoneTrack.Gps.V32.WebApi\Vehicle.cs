//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace VZoneTrack.Gps.V32.WebApi
{
    using System;
    using System.Collections.Generic;
    
    public partial class Vehicle
    {
        public long ID { get; set; }
        public long DeviceID { get; set; }
        public long DriverID { get; set; }
        public Nullable<int> CategoryID { get; set; }
        public string AssetCode { get; set; }
        public string Prefix { get; set; }
        public string DisplayText { get; set; }
        public string RegistrationNo { get; set; }
        public string Suffix { get; set; }
        public string PrefixSeperator { get; set; }
        public string SuffixSeperator { get; set; }
        public string Make { get; set; }
        public string Model { get; set; }
        public int FuelType { get; set; }
        public Nullable<decimal> EmptyVoltage { get; set; }
        public Nullable<decimal> FullVoltage { get; set; }
        public decimal FuelTankCapacity { get; set; }
        public Nullable<decimal> MaxSpeed { get; set; }
        public string Picture { get; set; }
        public string Comments { get; set; }
        public Nullable<System.DateTime> InstallationDate { get; set; }
        public Nullable<int> Mileage { get; set; }
        public Nullable<int> Odometer { get; set; }
        public Nullable<bool> IsActive { get; set; }
        public Nullable<bool> IsDelete { get; set; }
        public bool IsDoorSensor { get; set; }
        public Nullable<bool> IsTempSensor { get; set; }
        public bool IsSeatBelt { get; set; }
        public Nullable<bool> IsFuelTank { get; set; }
        public Nullable<long> CreatedBy { get; set; }
        public Nullable<System.DateTime> CreatedDate { get; set; }
        public Nullable<long> ModifiedBy { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<bool> IsRFIDEnabled { get; set; }
        public Nullable<bool> IsBarcodeEnabled { get; set; }
        public Nullable<bool> IsStudentPunch { get; set; }
        public Nullable<int> IdleDuration { get; set; }
        public Nullable<short> IsRFIDBasedDriver { get; set; }
        public Nullable<bool> IsSwipeOnStartRequired { get; set; }
        public bool IsKMCalcFromField { get; set; }
        public bool IsGadgetInstalled { get; set; }
        public Nullable<bool> IsAuthRFIDType { get; set; }
        public bool IsTeZEventDependsCardSwipe { get; set; }
        public bool IsBuzzerEnabled { get; set; }
        public bool IsImmobilization { get; set; }
        public bool IsCanBus { get; set; }
        public decimal TravelKMPerLtr { get; set; }
        public decimal IdleConsumptionPerHr { get; set; }
        public decimal EngineSize { get; set; }
        public int AllowedIdleDurInMin { get; set; }
        public int AllowedOverspeedDurInMin { get; set; }
        public int AllowedHarshAccCount { get; set; }
        public int AllowedHarshBrkCount { get; set; }
        public bool HasTrackerOdometer { get; set; }
        public Nullable<short> CurrentStalledStatus { get; set; }
        public Nullable<System.DateTime> StalledDueDate { get; set; }
        public bool IsChillerStatusEnabled { get; set; }
        public Nullable<short> ChillerInputPort { get; set; }
        public Nullable<short> JobIdentificationType { get; set; }
        public bool IsDOUTControlRequired { get; set; }
        public Nullable<short> DOUTControlPort { get; set; }
        public bool CurrentDOUTControlStatus { get; set; }
        public bool HasTempCardPunch { get; set; }
        public Nullable<System.DateTime> Odometer_SetOn { get; set; }
        public int ChillerTimeInMins { get; set; }
        public System.DateTime ChillerTime_SetOn { get; set; }
        public string Chiller_Remarks { get; set; }
        public string VIN { get; set; }
        public Nullable<int> ManufactureYear { get; set; }
        public string Trim { get; set; }
        public string Color { get; set; }
        public Nullable<decimal> MSRP { get; set; }
        public Nullable<int> EstimatedMonth { get; set; }
        public Nullable<int> EstimatedKM { get; set; }
        public Nullable<decimal> ResaleValue { get; set; }
        public bool IsEngineIdleEnabled { get; set; }
        public decimal ThresholdSupplyVoltage { get; set; }
        public int ConsDriftingDataOmitCount { get; set; }
        public bool IsDefaultDriftingFilterEnabled { get; set; }
        public bool IsCaseLevelAuthEnabled { get; set; }
        public bool IsCase_Authenticated { get; set; }
        public bool IsTrailer { get; set; }
        public bool IsHead { get; set; }
        public bool IsAttendanceDetailRequired { get; set; }
        public string AnalyticsJson { get; set; }
    }
}
