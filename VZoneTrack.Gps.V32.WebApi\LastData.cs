//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace VZoneTrack.Gps.V32.WebApi
{
    using System;
    using System.Collections.Generic;
    
    public partial class LastData
    {
        public long ID { get; set; }
        public long VehicleID { get; set; }
        public long TrackingID { get; set; }
        public string TraceCode { get; set; }
        public Nullable<decimal> Speed { get; set; }
        public Nullable<decimal> MovingDirection { get; set; }
        public Nullable<long> DriverID { get; set; }
        public Nullable<decimal> Latitude { get; set; }
        public Nullable<decimal> Longitude { get; set; }
        public string SIMNo { get; set; }
        public string Description { get; set; }
        public Nullable<System.DateTime> TrackTime { get; set; }
        public Nullable<System.DateTime> ServerTime { get; set; }
        public Nullable<int> Status { get; set; }
        public string ChargeStatus { get; set; }
        public Nullable<decimal> BatteryVoltage { get; set; }
        public Nullable<decimal> ChargeVoltage { get; set; }
        public Nullable<decimal> ADC0Collect { get; set; }
        public Nullable<decimal> ADC1Collect { get; set; }
        public Nullable<decimal> FuelSensor { get; set; }
        public Nullable<decimal> Temperature { get; set; }
        public Nullable<decimal> TotalMilemeter { get; set; }
        public decimal CurrentOdometer { get; set; }
        public Nullable<short> Job { get; set; }
        public string LocationName { get; set; }
        public Nullable<int> DoorSensor { get; set; }
        public string TrackEvent { get; set; }
        public Nullable<short> SeatBelt { get; set; }
        public Nullable<decimal> PDOP { get; set; }
        public Nullable<decimal> HDOP { get; set; }
        public Nullable<decimal> VDOP { get; set; }
        public Nullable<long> CompanyID { get; set; }
        public string LACCIC { get; set; }
        public string LACCIL { get; set; }
        public Nullable<short> FuelTank { get; set; }
        public string Alarm { get; set; }
        public string GPRMCState { get; set; }
        public string NS { get; set; }
        public string WE { get; set; }
        public string RFID { get; set; }
        public Nullable<bool> IsLandmark { get; set; }
        public Nullable<decimal> HighSpeed { get; set; }
        public Nullable<System.DateTime> FirstStartTime { get; set; }
        public Nullable<long> LandmarkID { get; set; }
        public string LandmarkName { get; set; }
        public Nullable<decimal> AwayAt { get; set; }
        public Nullable<int> TotTravel { get; set; }
        public Nullable<int> TotIdle { get; set; }
        public Nullable<int> TotStop { get; set; }
        public string TempTagList { get; set; }
        public string IButtonValue { get; set; }
        public string PropertyList { get; set; }
        public Nullable<System.DateTime> LastEventProcessedTime { get; set; }
    }
}
