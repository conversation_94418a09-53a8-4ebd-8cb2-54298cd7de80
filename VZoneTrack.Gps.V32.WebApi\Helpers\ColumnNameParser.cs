﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace VZoneTrack.Gps.V32.WebApi.Helpers
{
    public class ColumnNameParts
    {
        public string Alias { get; set; }
        public string SourceColumn { get; set; }
        public string TargetColumn { get; set; }
        public string TargetDataType { get; set; }
    }

    public static class ColumnNameParser
    {
        public static ColumnNameParts Parse(string name)
        {
            var parts = name.Split(new[] { ":", "->" }, StringSplitOptions.None);

            if (parts.Length < 3)
                throw new FormatException($"Invalid column Name format: '{name}'");

            var result = new ColumnNameParts
            {
                Alias = parts[0],
                SourceColumn = parts[1],
                TargetColumn = parts[2],
                TargetDataType = parts.Length >= 4 ? parts[3] : "object"
            };

            return result;
        }

        public static string Build(ColumnNameParts parts)
        {
            return $"{parts.Alias}:{parts.SourceColumn}->{parts.TargetColumn}:{parts.TargetDataType}";
        }
    }

}