//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace VZoneTrack.Gps.V32.WebApi
{
    using System;
    using System.Collections.Generic;
    
    public partial class Company
    {
        public long ID { get; set; }
        public string Code { get; set; }
        public string CompanyName { get; set; }
        public string ContactPerson { get; set; }
        public string Address { get; set; }
        public string PhoneNo { get; set; }
        public string MobileNo { get; set; }
        public string Email { get; set; }
        public string Url { get; set; }
        public bool IsPartner { get; set; }
        public string Logo { get; set; }
        public Nullable<System.DateTime> DBBackupTime { get; set; }
        public bool IsTeleCall { get; set; }
        public bool IsControlCentre { get; set; }
        public bool IsActive { get; set; }
        public bool IsDelete { get; set; }
        public Nullable<long> CreatedBy { get; set; }
        public System.DateTime CreatedDate { get; set; }
        public Nullable<long> UpdatedBy { get; set; }
        public System.DateTime UpdatedDate { get; set; }
        public decimal DefaultLat { get; set; }
        public decimal DefaultLong { get; set; }
        public string AssCompanyID { get; set; }
        public string DBName { get; set; }
        public Nullable<short> AllowVehicleMultipleGroup { get; set; }
        public bool IsDeliveryAppEnabled { get; set; }
        public bool IsVZone4DEnabled { get; set; }
        public int DefaultZoomLevel { get; set; }
        public bool IsTempDashboardEnabled { get; set; }
        public bool IsFleetInfoSystemEnabled { get; set; }
        public bool IsPremiumAccount { get; set; }
        public bool IsUnderTrial { get; set; }
        public Nullable<System.DateTime> TrialExpiryOn { get; set; }
        public string CustomMessage { get; set; }
        public Nullable<System.DateTime> CustomMessageExpiryOn { get; set; }
        public string TimeZone { get; set; }
        public bool HasPassengerTracking { get; set; }
        public bool IsRequireStalledListAlert { get; set; }
        public int MaxStorageInMB { get; set; }
        public int UtilizedStorageInKB { get; set; }
        public string CancellationReason { get; set; }
        public bool HHTConfigEnabled { get; set; }
        public string HHTCodeRange { get; set; }
        public bool IsCaseLevelAuthFeatureActivated { get; set; }
        public int ApiRateLimit { get; set; }
    }
}
