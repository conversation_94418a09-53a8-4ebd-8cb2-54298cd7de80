//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace VZoneTrack.Gps.V32.WebApi
{
    using System;
    
    public partial class VehiclesInCompany
    {
        public int Id { get; set; }
        public Nullable<int> VehicleId { get; set; }
        public string AssetCode { get; set; }
        public string DisplayText { get; set; }
        public string RegistrationNo { get; set; }
        public string Make { get; set; }
        public string Model { get; set; }
        public string FuelType { get; set; }
        public Nullable<decimal> FuelTankCapacity { get; set; }
        public Nullable<int> AllowedSpeed { get; set; }
        public Nullable<System.DateTime> InstallationDate { get; set; }
        public Nullable<int> InitialOdometer { get; set; }
        public Nullable<System.DateTime> InitialOdometerSetDate { get; set; }
        public Nullable<bool> TempSensorEnabled { get; set; }
        public Nullable<bool> DoorSensorEnabled { get; set; }
        public Nullable<bool> CanBusEnabled { get; set; }
        public Nullable<bool> ChillerEnabled { get; set; }
        public Nullable<bool> EngineIdleDetectEnabled { get; set; }
        public Nullable<bool> CaseAuthEnabled { get; set; }
        public Nullable<decimal> TravelKMPerLitre { get; set; }
        public Nullable<decimal> IdleConsPerLitre { get; set; }
        public Nullable<bool> IsActive { get; set; }
    }
}
