﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Data;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Http.Description;
using VZoneTrack.Gps.V32.WebApi.Code;

namespace VZoneTrack.Gps.V32.WebApi.Controllers
{
    [Authorize]
    public class IntegrationController : ControllerBase
    {
        [HttpPost]
        [Route("AlAinFarms/HHTLogs")]
        public IHttpActionResult HHTLogsAlAinFarms([FromBody] JToken RequestParams)
        {
            string userName = GetUserDetails();
            string apiType = GetApiType();
            string requestType = GetRequestType();
            bool _is2FAVerified = TwoFactorAuthenticateUser(userName, apiType);

            if (apiType == "external" && requestType == "other" && userName != null && _is2FAVerified)
            {
                IntegrationMiddleware middleWare = new IntegrationMiddleware();
                //int result = middleWare.ProcessHHTLogAlAinFarms(RequestParams);
                int result = middleWare.ProcessHHTLogToQueue(RequestParams);
                return Ok(new
                {
                    message = "SUCCESS",
                    data = result.ToString() + " records processed"
                });
            }
            return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.Unauthorized, "Unauthorized User!"));
        }
    }
}
