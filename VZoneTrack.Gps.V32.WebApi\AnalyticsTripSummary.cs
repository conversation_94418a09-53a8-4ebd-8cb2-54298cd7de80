//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace VZoneTrack.Gps.V32.WebApi
{
    using System;
    using System.Collections.Generic;
    
    public partial class AnalyticsTripSummary
    {
        public int TripSummaryId { get; set; }
        public int CompanyId { get; set; }
        public int VehicleId { get; set; }
        public System.DateTime TripDate { get; set; }
        public Nullable<decimal> TopSpeed { get; set; }
        public Nullable<decimal> AvgSpeed { get; set; }
        public Nullable<int> TravelDurationMin { get; set; }
        public Nullable<int> IdleDurationMin { get; set; }
        public Nullable<int> StopDurationMin { get; set; }
        public Nullable<int> TotalDurationMin { get; set; }
        public Nullable<decimal> FuelCons { get; set; }
        public Nullable<decimal> FuelCost { get; set; }
        public Nullable<int> IdleViolationDurationMin { get; set; }
        public Nullable<decimal> IdleViolationFuelCons { get; set; }
        public Nullable<decimal> IdleViolationFuelCost { get; set; }
        public Nullable<decimal> DistanceTraveled { get; set; }
        public string AlertSummaryJson { get; set; }
        public string DoorSummaryJson { get; set; }
        public string TemperatureSummaryJson { get; set; }
        public string SwipeSummaryJson { get; set; }
        public string ChillerSummaryJson { get; set; }
    }
}
