﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net461" />
  <package id="bootstrap" version="3.4.1" targetFramework="net461" />
  <package id="EntityFramework" version="6.5.1" targetFramework="net48" />
  <package id="ExtendedNumerics.BigDecimal" version="3000.0.3.40" targetFramework="net48" />
  <package id="Flee" version="2.0.0" targetFramework="net48" />
  <package id="jQuery" version="3.4.1" targetFramework="net461" />
  <package id="log4net" version="2.0.14" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.HelpPage" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Owin" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net461" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Configuration" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Configuration" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Console" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.TraceSource" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.Extensions.Options" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.Extensions.Options.ConfigurationExtensions" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.Extensions.Primitives" version="9.0.4" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="6.15.1" targetFramework="net461" requireReinstallation="true" />
  <package id="Microsoft.IdentityModel.Logging" version="6.15.1" targetFramework="net461" requireReinstallation="true" />
  <package id="Microsoft.IdentityModel.Tokens" version="6.15.1" targetFramework="net461" requireReinstallation="true" />
  <package id="Microsoft.NETCore.Platforms" version="1.1.0" targetFramework="net48" />
  <package id="Microsoft.Owin" version="4.2.0" targetFramework="net461" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="4.2.0" targetFramework="net461" />
  <package id="Microsoft.Owin.Security" version="4.2.0" targetFramework="net461" />
  <package id="Microsoft.Owin.Security.Jwt" version="4.2.0" targetFramework="net461" />
  <package id="Microsoft.Owin.Security.OAuth" version="4.2.0" targetFramework="net461" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net461" />
  <package id="Modernizr" version="2.8.3" targetFramework="net461" />
  <package id="NETStandard.Library" version="2.0.3" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net461" />
  <package id="Owin" version="1.0" targetFramework="net461" />
  <package id="Parlot" version="1.3.5" targetFramework="net48" />
  <package id="RabbitMQ.Client" version="6.8.1" targetFramework="net48" />
  <package id="System.Buffers" version="4.6.0" targetFramework="net48" />
  <package id="System.Collections.Immutable" version="9.0.4" targetFramework="net48" />
  <package id="System.ComponentModel" version="4.3.0" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="9.0.4" targetFramework="net48" />
  <package id="System.IdentityModel.Tokens.Jwt" version="6.15.1" targetFramework="net461" requireReinstallation="true" />
  <package id="System.IO.Pipelines" version="9.0.4" targetFramework="net48" />
  <package id="System.Memory" version="4.6.0" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.6.0" targetFramework="net48" />
  <package id="System.Reflection" version="4.3.0" targetFramework="net48" />
  <package id="System.Reflection.Emit" version="4.7.0" targetFramework="net48" />
  <package id="System.Reflection.Emit.ILGeneration" version="4.7.0" targetFramework="net48" />
  <package id="System.Reflection.Emit.Lightweight" version="4.7.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="9.0.4" targetFramework="net48" />
  <package id="System.Text.Json" version="9.0.4" targetFramework="net48" />
  <package id="System.Threading.Channels" version="8.0.0" targetFramework="net48" />
  <package id="System.Threading.RateLimiting" version="8.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.6.1" targetFramework="net48" />
  <package id="Unity" version="5.11.10" targetFramework="net461" requireReinstallation="true" />
  <package id="WebGrease" version="1.6.0" targetFramework="net461" />
</packages>