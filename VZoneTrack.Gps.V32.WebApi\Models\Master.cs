﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace VZoneTrack.Gps.V32.WebApi.Models
{
    public class VehicleInfo
    {
        public int VehicleId { get; set; }
        public string AssetCode { get; set; }
        public string Category { get; set; }
        public string DisplayText { get; set; }
        public string PlateNo { get; set; }
        public string Model { get; set; }
        public string FuelType { get; set; }
        public string TankCapcity { get; set; }
        public string AllowedSpeed { get; set; }
        public string GPSInstallationDate { get; set; }
        public Accessories Accessories { get; set; }
        public string VIN { get; set; }
        public string YOM { get; set; }
        public string Trim { get; set; }
        public string Color { get; set; }
        public string MSRP { get; set; }
    }

    public class Accessories
    {
        public bool Authentication { get; set; }
        public bool Buzzer { get; set; }
        public bool Immobilization { get; set; }
        public bool CANBUS { get; set; }
        public bool Chiller { get; set; }
        public bool Door { get; set; }
        public bool Gadget { get; set; }
        public bool Seatbelt { get; set; }
        public bool Temperature { get; set; }

    }


    public class JobInfo
    {   
        public string CustomerName { get; set; }
        public string IMEI { get; set; }
        public string RegistrationNo { get; set; }
        public string VehicleType { get; set; }
        public string Make { get; set; }
        public string Model { get; set; }
        public int Odometer { get; set; }
        public string Accessories { get; set; }
        public int IButtonReaderCount { get; set; }
        public int BuzzerCount { get; set; }
        public int RFIDCount { get; set; }
        public DateTime DateOfJob { get; set; }
    }

    public class DeviceInfo
    {
        public string IMEI { get; set; }
        public DateTime ProgrammedOn { get; set; }
        public string Type { get; set; }
        public string SIM { get; set; }
        public string SIMICCID { get; set; }
    }

    public class VehicleBasicInfo
    {
        public int VehicleId { get; set; }     
        public string DisplayText { get; set; }
        public string PlateNo { get; set; }
        public List<GroupInfo> Groups { get; set; }
    }

    public class GroupInfo
    {
        public long GroupId { get; set; }
        public string GroupName { get; set; }
        public bool IsSystemDefined { get; set; }
    }

}