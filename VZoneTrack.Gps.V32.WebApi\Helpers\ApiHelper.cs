﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web;

namespace VZoneTrack.Gps.V32.WebApi.Helpers
{
    public static class ApiHelper
    {
        private static HttpClient client;

        public static void Init(string baseUri)
        {
            client = new HttpClient
            {
                BaseAddress = new Uri(baseUri)
            };
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.Timeout = TimeSpan.FromSeconds(30);
        }

        public static async Task<string> GetDataAsync(string apiEndpoint)
        {
            if (client == null) throw new InvalidOperationException("ApiHelper not initialized.");

            var response = await client.GetAsync(apiEndpoint);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }

        public static async Task<string> PostDataAsync(string apiEndpoint, HttpContent content)
        {
            if (client == null) throw new InvalidOperationException("ApiHelper not initialized.");

            var response = await client.PostAsync(apiEndpoint, content);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }
    }

}