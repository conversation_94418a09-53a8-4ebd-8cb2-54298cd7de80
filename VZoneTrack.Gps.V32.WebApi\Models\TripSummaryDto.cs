﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace VZoneTrack.Gps.V32.WebApi.Models
{
    public class TripSummaryDto
    {
        public int VehicleId { get; set; }
        public DateTime TripDate { get; set; }
        public double TopSpeed { get; set; }
        public double AvgSpeed { get; set; }
        public int TravelDurationMin { get; set; }
        public int IdleDurationMin { get; set; }
        public int StopDurationMin { get; set; }
        public int TotalDurationMin { get; set; }
        public double FuelCons { get; set; }
        public double FuelCost { get; set; }
        public int IdleViolationDurationMin { get; set; }
        public double DistanceTraveled { get; set; }
    }

}