//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace VZoneTrack.Gps.V32.WebApi
{
    using System;
    using System.Collections.Generic;
    
    public partial class AnalyticsTripDetail
    {
        public int TripDetailId { get; set; }
        public int TripSummaryId { get; set; }
        public int CompanyId { get; set; }
        public int VehicleId { get; set; }
        public Nullable<int> DriverId { get; set; }
        public string StartLocation { get; set; }
        public Nullable<decimal> StartLatitude { get; set; }
        public Nullable<decimal> StartLongitude { get; set; }
        public Nullable<System.DateTime> StartTime { get; set; }
        public string EndLocation { get; set; }
        public Nullable<decimal> EndLatitude { get; set; }
        public Nullable<decimal> EndLongitude { get; set; }
        public Nullable<System.DateTime> EndTime { get; set; }
        public Nullable<int> TravelDurationMin { get; set; }
        public Nullable<int> IdleDurationMin { get; set; }
        public Nullable<int> TripDurationMin { get; set; }
        public Nullable<decimal> FuelCons { get; set; }
        public Nullable<decimal> FuelCost { get; set; }
        public Nullable<decimal> DistanceTraveled { get; set; }
        public string AlertDetailJson { get; set; }
        public string DoorDetailJson { get; set; }
        public string TemperatureDetailJson { get; set; }
        public string SwipeDetailJson { get; set; }
        public string ChillerDetailJson { get; set; }
    }
}
