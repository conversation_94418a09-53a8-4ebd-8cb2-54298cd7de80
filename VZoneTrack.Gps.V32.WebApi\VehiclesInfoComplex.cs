//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace VZoneTrack.Gps.V32.WebApi
{
    using System;
    
    public partial class VehiclesInfoComplex
    {
        public int Id { get; set; }
        public Nullable<long> VehicleId { get; set; }
        public Nullable<long> DeviceId { get; set; }
        public Nullable<int> TypeId { get; set; }
        public string DeviceIMEI { get; set; }
        public string SIMNO { get; set; }
        public Nullable<int> ManufactureId { get; set; }
        public string AssetCode { get; set; }
        public string DisplayText { get; set; }
        public string PlateNo { get; set; }
        public string Model { get; set; }
        public Nullable<decimal> MaxSpeed { get; set; }
        public Nullable<int> CategoryId { get; set; }
        public string CategoryName { get; set; }
        public string CategoryImage { get; set; }
        public Nullable<bool> IsTempSensor { get; set; }
        public Nullable<bool> IsRFIDEnabled { get; set; }
        public Nullable<bool> IsDoorSensor { get; set; }
        public Nullable<bool> IsSeatbelt { get; set; }
        public Nullable<bool> IsAnyTemperatureEnabled { get; set; }
        public Nullable<int> IdleDataInterval { get; set; }
        public Nullable<int> TimeZone { get; set; }
        public Nullable<bool> IsGadgetInstalled { get; set; }
        public Nullable<bool> IsCANBUSEnabled { get; set; }
        public Nullable<decimal> FuelTankCapacity { get; set; }
        public Nullable<short> CurrentStalledStatus { get; set; }
        public Nullable<short> ChillerInputPort { get; set; }
        public Nullable<bool> IsChillerStatusEnabled { get; set; }
        public Nullable<bool> IsEngineIdleEnabled { get; set; }
        public Nullable<decimal> ThresholdSupplyVoltage { get; set; }
        public Nullable<int> ConsDriftingDataOmitCount { get; set; }
        public Nullable<bool> IsDefaultDriftingFilterEnabled { get; set; }
        public Nullable<bool> IsDoorSensorEnabled { get; set; }
        public string FuelType { get; set; }
        public string Color { get; set; }
        public string VIN { get; set; }
        public Nullable<int> ManufactureYear { get; set; }
        public Nullable<decimal> MSRP { get; set; }
        public string VehicleTrim { get; set; }
        public Nullable<System.DateTime> InstallationDate { get; set; }
        public Nullable<bool> IsBuzzerEnabled { get; set; }
        public Nullable<bool> IsImmobilizationEnabled { get; set; }
    }
}
