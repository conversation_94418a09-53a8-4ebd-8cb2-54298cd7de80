﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace VZoneTrack.Gps.V32.WebApi.Models
{ 

    public class Itinerary
    {
        public string AssetCode { get; set; }
        public string Vehicle { get; set; }
        public List<Trip> Trips { get; set; }
        public List<Swipe> SwipeInfo { get; set; }
    }

    public class Location
    {
        public string Name { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
    }

    public class Swipe
    {
        public Personnel Personnel { get; set; }        
        public DateTime SwipedAt { get; set; }
        public Location Location { get; set; }
    }

    public class Trip
    {
        public Personnel Operator { get; set; }        
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public Location StartLocation { get; set; }
        public Location EndLocation { get; set; }
        //public DateTime SwipeAt { get; set; }
        public double Distance { get; set; }
        public int TravelDurationInMin { get; set; }
        public int IdleDurationInMin { get; set; }
        public double FuelConsLtr { get; set; }
        public double FuelCost { get; set; }
        public int TopSpeed { get; set; }
        public double AvgSpeed { get; set; }
    }

    public class Personnel
    {
        public string Code { get; set; }       
        public string Name { get; set; }
        public string CardNumber { get; set; }
    }

}