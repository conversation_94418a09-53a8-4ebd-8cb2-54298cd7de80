﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using VZoneTrack.Gps.V32.WebApi.Interfaces;
using VZoneTrack.Gps.V32.WebApi.Models;
using VZoneTrack.Gps.V32.Business;
using System.Data;
using System.Reflection;
using System.Globalization;
using VZoneTrack.Gps.V32.WebApi.Code;

namespace VZoneTrack.Gps.V32.WebApi.Controllers
{
    [Authorize]
    public class TrackingController : ControllerBase
    {
        private readonly RateLimiter rateLimiter;

        public TrackingController()
        {
            this.rateLimiter = new RateLimiter();
        }

        [HttpGet]
        [Route("Tracking/OdometerInfo/{AssetCode}/{DataDateTime?}")]
        public IHttpActionResult GetOdometerInfo([FromUri] string AssetCode, [FromUri] string DataDateTime = "") //DateTime Format {yyyyMMddHHmmss}
        {
            string userName = GetUserDetails();
            string apiType = GetApiType();
            string requestType = GetRequestType();
            bool _is2FAVerified = TwoFactorAuthenticateUser(userName, apiType);

            if (rateLimiter.TryAcquire(userName))
            {
                if (apiType == "internal" && requestType == "AppLink" && userName == "vzoneapps" && _is2FAVerified)
                {
                    long _vehicleId = EntityProvider.GetVehicleIdFromAssetCode(AssetCode);
                    DateTime _requestDateTime;
                    if (DataDateTime == "")
                    {
                        _requestDateTime = EntityProvider.CurrentVehicleTime(Convert.ToInt32(_vehicleId));
                    }
                    else
                    {
                        _requestDateTime = DateTime.ParseExact(DataDateTime, "yyyyMMddHHmmss", null);
                    }

                    DashboardBusiness dashboardBusiness = new DashboardBusiness();
                    object[] result = dashboardBusiness.FetchOdometerFromVehicleId((int)_vehicleId, _requestDateTime);

                    return Ok(new
                    {
                        message = "SUCCESS",
                        data = result
                    });
                }
                return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.Unauthorized, "Unauthorized User!"));
            }
            else
            {
                return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.OK, "Rate limit exceeded"));
            }
        }

        [HttpGet]
        [Route("Tracking/ReeferInfo/{AssetCode}/{DataDateTime?}")]
        public IHttpActionResult GetReeferInfo([FromUri] string AssetCode, [FromUri] string DataDateTime = "") //DateTime Format {yyyyMMddHHmmss}
        {
            string userName = GetUserDetails();
            string apiType = GetApiType();
            string requestType = GetRequestType();
            bool _is2FAVerified = TwoFactorAuthenticateUser(userName, apiType);

            if (rateLimiter.TryAcquire(userName))
            {
                if (apiType == "internal" && requestType == "AppLink" && userName == "vzoneapps" && _is2FAVerified)
                {
                    long _vehicleId = EntityProvider.GetVehicleIdFromAssetCode(AssetCode);
                    DateTime? _requestDateTime;
                    if (DataDateTime == "")
                    {
                        _requestDateTime = null;
                    }
                    else
                    {
                        _requestDateTime = DateTime.ParseExact(DataDateTime, "yyyyMMddHHmmss", null);
                    }
                    VehicleBusiness vehicleBusiness = new VehicleBusiness();
                    DataSet dsResult = vehicleBusiness.GetReeferInfo((int)_vehicleId, _requestDateTime);
                    DataTable result = dsResult.Tables[0];

                    return Ok(new
                    {
                        message = "SUCCESS",
                        data = result
                    });
                }
                return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.Unauthorized, "Unauthorized User!"));
            }
            else
            {
                return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.OK, "Rate limit exceeded"));
            }
        }

        [HttpGet]
        [Route("Tracking/Trips/{AssetCode}/{TripStartDate?}/{TripEndDate?}")]
        public IHttpActionResult GetTrips([FromUri] string AssetCode, [FromUri] string TripStartDate = "", [FromUri] string TripEndDate = "") //DateTime Format {yyyyMMdd}
        {
            string userName = GetUserDetails();
            string apiType = GetApiType();
            string requestType = GetRequestType();
            bool _is2FAVerified = TwoFactorAuthenticateUser(userName, apiType);

            if (rateLimiter.TryAcquire(userName))
            {
                if (apiType == "external" && requestType == "other" && userName != null && _is2FAVerified)
                {
                    long _companyId = EntityProvider.GetCompanyIdFromUsername(userName);
                    Vehicle vehicle = EntityProvider.GetVehicleInfoFromAssetCode(AssetCode);
                    Itinerary result = null;

                    DateTime? _startDate;
                    if (string.IsNullOrEmpty(TripStartDate))
                    {
                        _startDate = DateTime.UtcNow.Date;
                    }
                    else
                    {
                        _startDate = DateTime.ParseExact(TripStartDate, "yyyyMMdd", null);
                    }

                    DateTime? _endDate;
                    if (string.IsNullOrEmpty(TripEndDate))
                    {
                        _endDate = DateTime.UtcNow.Date;
                    }
                    else
                    {
                        _endDate = DateTime.ParseExact(TripEndDate, "yyyyMMdd", null);
                    }

                    if (_startDate != null && _endDate != null)
                    {
                        DateTime _tripStartDate = Convert.ToDateTime(_startDate);
                        DateTime _tripEndDate = Convert.ToDateTime(_endDate).Add(new TimeSpan(23, 59, 59));

                        TrackingMiddleware trackingMiddleware = new TrackingMiddleware();
                        result = trackingMiddleware.ProcessTrips(vehicle, (int)_companyId, _tripStartDate, _tripEndDate);
                    }

                    return Ok(new
                    {
                        message = "SUCCESS",
                        data = result
                    });
                }
                return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.Unauthorized, "Unauthorized User!"));
            }
            else
            {
                return ResponseMessage(Request.CreateErrorResponse(HttpStatusCode.OK, "Rate limit exceeded"));                
            }           
        }
    }
}