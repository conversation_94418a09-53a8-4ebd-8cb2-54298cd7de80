using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using Flee.PublicTypes;
using log4net;
using Newtonsoft.Json;
using VZoneTrack.Gps.V32.WebApi.Code;
using VZoneTrack.Gps.V32.WebApi.Helpers;
using VZoneTrack.Gps.V32.WebApi.Interfaces;
using VZoneTrack.Gps.V32.WebApi.Models;
using VZoneTrack.Gps.V32.WebApi.Services;

namespace VZoneTrack.Gps.V32.WebApi.Controllers
{
    [Authorize]
    public class VZoneGenieController : ControllerBase
    {
        private readonly IVZoneGenieService _vzoneGenieService;
        private static readonly ILog _logger = LogManager.GetLogger(typeof(VZoneGenieController));

        public VZoneGenieController()
        {
            _vzoneGenieService = new VZoneGenieService();
        }

        /// <summary>
        /// Processes a VZoneGenie request asynchronously, handling Summary or Live request types.
        /// </summary>
        /// <param name="request">The VZoneGenie request containing operations and parameters.</param>
        /// <returns>An HTTP action result with the processed data or an error response.</returns>
        [AuthorizeAndRateLimit]
        [HttpPost]
        [Route("VZoneGenie/Analytics/Process")]
        public async Task<IHttpActionResult> ProcessAsync([FromBody] VZoneGenieRequest request)
        {
            try
            {
                long companyId = GetCompanyId();                
                if (request.RequestType == RequestType.Summary)
                {
                    if (request.Operations == null || !request.Operations.Any())
                        return BadRequest("Missing required operations.");

                    var results = await ProcessSummaryRequestAsync(request, companyId);
                    return Ok(results);
                }
                else if (request.RequestType == RequestType.Live)
                {
                    var results = await ProcessLiveRequestAsync(request, companyId);
                    return Ok(results);
                }
                else if(request.RequestType == RequestType.Route)
                {
                    int userId = GetUserId();
                    var results = await ProcessRouteRequestAsync(request, companyId, userId);
                    return Ok(results);
                }
                else if (request.RequestType == RequestType.Detail)
                {
                    return Ok("Detail");
                }

                return BadRequest("Invalid RequestType.");
            }
            catch (Exception ex)
            {
                _logger.Error(string.Format("Error in ProcessAsync - Request: {0}", JsonConvert.SerializeObject(request)), ex);
                return InternalServerError(new Exception("An unexpected error occurred while processing your request. Please contact support."));
            }
        }

        /// <summary>
        /// Retrieves the company ID from the request properties.
        /// </summary>
        /// <returns>The company ID, or 0 if not found.</returns>
        private long GetCompanyId()
        {
            return Request.Properties.ContainsKey("CompanyId") ? (long)Request.Properties["CompanyId"] : 0;
        }

        /// <summary>
        /// Retrieves the company ID from the request properties.
        /// </summary>
        /// <returns>The company ID, or 0 if not found.</returns>
        private int GetUserId()
        {
            return Request.Properties.ContainsKey("UserId") ? (int)Request.Properties["UserId"] : 0;
        }

        /// <summary>
        /// Processes a Summary request by iterating over vehicles and operations.
        /// </summary>
        /// <param name="request">The VZoneGenie request.</param>
        /// <param name="companyId">The company ID.</param>
        /// <returns>A dictionary mapping vehicle IDs to their operation results.</returns>
        private async Task<object> ProcessSummaryRequestAsync(VZoneGenieRequest request, long companyId)
        {
            if (request.VehicleIds != null && request.VehicleIds.Any())
            {
                var results = new Dictionary<int, Dictionary<string, object>>();

                foreach (var vehicleId in request.VehicleIds)
                {
                    var vehicleResults = new Dictionary<string, object>();

                    foreach (var operation in request.Operations)
                    {
                        switch (operation.OperationType)
                        {
                            case OperationType.Raw:
                                await ProcessRawOperationAsync(operation, vehicleId, companyId, vehicleResults, request.From, request.To);
                                break;
                            case OperationType.Aggregate:
                                await ProcessAggregateOperationAsync(operation, vehicleId, companyId, vehicleResults, request.From, request.To);
                                break;
                            case OperationType.Compute:
                                await ProcessComputeOperationAsync(operation, vehicleId, companyId, vehicleResults, request.From, request.To);
                                break;
                            case OperationType.Lookup:
                                await ProcessLookupOperationAsync(operation, vehicleId, companyId, vehicleResults, request.From, request.To);
                                break;
                        }
                    }

                    results[vehicleId] = vehicleResults;
                }

                return results;
            }
            else
            {   
                var vehicleResults = new Dictionary<string, object>();

                foreach (var operation in request.Operations)
                {
                    switch (operation.OperationType)
                    {
                        case OperationType.Raw:
                            await ProcessRawOperationAsync(operation, 0, companyId, vehicleResults, request.From, request.To);
                            break;
                        case OperationType.Aggregate:
                            await ProcessAggregateOperationAsync(operation, 0, companyId, vehicleResults, request.From, request.To);
                            break;
                        case OperationType.Compute:
                            await ProcessComputeOperationAsync(operation, 0, companyId, vehicleResults, request.From, request.To);
                            break;
                        case OperationType.Lookup:
                            await ProcessLookupOperationAsync(operation, 0, companyId, vehicleResults, request.From, request.To);
                            break;
                    }
                }

                return vehicleResults;
            }
        }

        /// <summary>
        /// Processes a Live request by fetching live data for each vehicle.
        /// </summary>
        /// <param name="request">The VZoneGenie request.</param>
        /// <param name="companyId">The company ID.</param>
        /// <returns>A dictionary mapping vehicle IDs to their live data results.</returns>
        private async Task<Dictionary<int, Dictionary<string, object>>> ProcessLiveRequestAsync(VZoneGenieRequest request, long companyId)
        {
            var results = new Dictionary<int, Dictionary<string, object>>();

            foreach (var vehicleId in request.VehicleIds)
            {
                var operation = request.Operations.FirstOrDefault();
                var liveResult = await _vzoneGenieService.GetLiveDataAsync(operation != null ? operation.LookupOperations : null, vehicleId, companyId);
                results[vehicleId] = liveResult;
            }

            return results;
        }


        private async Task<Dictionary<int, Dictionary<string, object>>> ProcessRouteRequestAsync(VZoneGenieRequest request, long companyId, int userId)
        {
            var results = new Dictionary<int, Dictionary<string, object>>();

            foreach (var vehicleId in request.VehicleIds)
            {
                var operation = request.Operations.FirstOrDefault();
                var liveResult = await _vzoneGenieService.GenerateVehicleRouteKmlAsync(request.From, request.To, vehicleId, companyId, userId);
                results[vehicleId] = liveResult;
            }

            return results;
        }


        /// <summary>
        /// Processes a Raw operation by fetching raw data for each column operation.
        /// </summary>
        /// <param name="operation">The operation request.</param>
        /// <param name="vehicleId">The vehicle ID.</param>
        /// <param name="companyId">The company ID.</param>
        /// <param name="vehicleResults">The dictionary to store results.</param>
        /// <param name="from">The start date.</param>
        /// <param name="to">The end date.</param>
        private async Task ProcessRawOperationAsync(OperationRequest operation, int vehicleId, long companyId, Dictionary<string, object> vehicleResults, DateTime? from, DateTime? to)
        {
            foreach (var columnOp in operation.ColumnOperations)
            {
                var type = TypeHelper.GetTypeFromName(columnOp.SourceDataType);
                var method = typeof(VZoneGenieService).GetMethod("GetRawDataAsync");
                if (method == null)
                {
                    _logger.Error("GetRawDataAsync method not found.");
                    vehicleResults[columnOp.ResponseText] = null;
                    continue;
                }

                var genericMethod = method.MakeGenericMethod(type);
                var task = (Task)genericMethod.Invoke(_vzoneGenieService, new object[] {
                    columnOp.SourceColumn,
                    from,
                    to,
                    vehicleId,
                    companyId
                });
                await task.ConfigureAwait(false);
                var resultProperty = task.GetType().GetProperty("Result");
                var result = resultProperty != null ? resultProperty.GetValue(task) : null;
                vehicleResults[columnOp.ResponseText] = result;
            }
        }

        /// <summary>
        /// Processes an Aggregate operation by computing aggregates for each column operation.
        /// </summary>
        /// <param name="operation">The operation request.</param>
        /// <param name="vehicleId">The vehicle ID.</param>
        /// <param name="companyId">The company ID.</param>
        /// <param name="vehicleResults">The dictionary to store results.</param>
        /// <param name="from">The start date.</param>
        /// <param name="to">The end date.</param>
        private async Task ProcessAggregateOperationAsync(OperationRequest operation, int vehicleId, long companyId, Dictionary<string, object> vehicleResults, DateTime? from, DateTime? to)
        {
            foreach (var columnOp in operation.ColumnOperations)
            {
                var type = TypeHelper.GetTypeFromName(columnOp.SourceDataType);
                var method = typeof(VZoneGenieService).GetMethod("GetAggregateDataAsync");
                if (method == null)
                {
                    _logger.Error("GetAggregateDataAsync method not found.");
                    vehicleResults[columnOp.ResponseText] = null;
                    continue;
                }

                var genericMethod = method.MakeGenericMethod(type);
                var task = (Task)genericMethod.Invoke(_vzoneGenieService, new object[] {
                    columnOp.AggregateFunction,
                    columnOp.SourceColumn,
                    from,
                    to,
                    vehicleId,
                    companyId
                });
                await task.ConfigureAwait(false);
                var resultProperty = task.GetType().GetProperty("Result");
                var result = resultProperty != null ? resultProperty.GetValue(task) : null;
                vehicleResults[columnOp.ResponseText] = result;
            }
        }

        /// <summary>
        /// Processes a Compute operation by evaluating formulas for each column operation.
        /// </summary>
        /// <param name="operation">The operation request.</param>
        /// <param name="vehicleId">The vehicle ID.</param>
        /// <param name="companyId">The company ID.</param>
        /// <param name="vehicleResults">The dictionary to store results.</param>
        /// <param name="from">The start date.</param>
        /// <param name="to">The end date.</param>
        private async Task ProcessComputeOperationAsync(OperationRequest operation, int vehicleId, long companyId, Dictionary<string, object> vehicleResults, DateTime? from, DateTime? to)
        {
            foreach (var columnOp in operation.ColumnOperations)
            {
                var parameters = await FetchComputeParametersAsync(columnOp.Calculation.Operands, vehicleId, companyId, from, to);

                var context = new ExpressionContext();
                context.Options.CaseSensitive = false;
                foreach (var param in parameters)
                {
                    context.Variables[param.Key] = param.Value;
                }

                try
                {
                    var expr = context.CompileDynamic(columnOp.Calculation.Formula);
                    var calcResult = expr.Evaluate();

                    if (calcResult is double)
                    {
                        calcResult = Math.Round((double)calcResult, 2);
                    }
                    else if (calcResult is decimal)
                    {
                        calcResult = Math.Round((decimal)calcResult, 2);
                    }

                    vehicleResults[columnOp.ResponseText] = calcResult;
                }
                catch (ExpressionCompileException ex)
                {
                    _logger.Error(string.Format("Formula compile error for {0}: {1}", columnOp.ResponseText, ex.Message), ex);
                    vehicleResults[columnOp.ResponseText] = null;
                }
                catch (Exception ex)
                {
                    _logger.Error(string.Format("Formula evaluation error for {0}: {1}", columnOp.ResponseText, ex.Message), ex);
                    vehicleResults[columnOp.ResponseText] = null;
                }
            }
        }

        /// <summary>
        /// Fetches parameters for a Compute operation by retrieving operand values.
        /// </summary>
        /// <param name="operands">The list of operands.</param>
        /// <param name="vehicleId">The vehicle ID.</param>
        /// <param name="companyId">The company ID.</param>
        /// <param name="from">The start date.</param>
        /// <param name="to">The end date.</param>
        /// <returns>A dictionary of operand names and their values.</returns>
        private async Task<Dictionary<string, object>> FetchComputeParametersAsync(List<CalculationOperand> operands, int vehicleId, long companyId, DateTime? from, DateTime? to)
        {
            var parameters = new Dictionary<string, object>();

            foreach (var operand in operands)
            {
                var type = TypeHelper.GetTypeFromName(operand.DataType);
                if (operand.Operation == OperationType.Aggregate)
                {
                    var method = typeof(VZoneGenieService).GetMethod("GetAggregateDataAsync");
                    if (method == null)
                    {
                        _logger.Error("GetAggregateDataAsync method not found for operand: " + operand.Name);
                        parameters[operand.Name] = null;
                        continue;
                    }

                    var genericMethod = method.MakeGenericMethod(type);
                    var task = (Task)genericMethod.Invoke(_vzoneGenieService, new object[] {
                        operand.AggregateFunction,
                        operand.Name,
                        from,
                        to,
                        vehicleId,
                        companyId
                    });
                    await task.ConfigureAwait(false);
                    var resultProperty = task.GetType().GetProperty("Result");
                    var value = resultProperty != null ? resultProperty.GetValue(task) : null;
                    parameters[operand.Name] = value;
                }
                else if (operand.Operation == OperationType.Raw)
                {
                    var method = typeof(VZoneGenieService).GetMethod("GetRawDataAsync");
                    if (method == null)
                    {
                        _logger.Error("GetRawDataAsync method not found for operand: " + operand.Name);
                        parameters[operand.Name] = null;
                        continue;
                    }

                    var genericMethod = method.MakeGenericMethod(type);
                    var task = (Task)genericMethod.Invoke(_vzoneGenieService, new object[] {
                        operand.Name,
                        from,
                        to,
                        vehicleId,
                        companyId
                    });
                    await task.ConfigureAwait(false);
                    var resultProperty = task.GetType().GetProperty("Result");
                    var value = resultProperty != null ? resultProperty.GetValue(task) : null;
                    parameters[operand.Name] = value;
                }
            }

            return parameters;
        }

        /// <summary>
        /// Processes a Lookup operation by fetching lookup data for each lookup operation.
        /// </summary>
        /// <param name="operation">The operation request.</param>
        /// <param name="vehicleId">The vehicle ID.</param>
        /// <param name="companyId">The company ID.</param>
        /// <param name="vehicleResults">The dictionary to store results.</param>
        /// <param name="from">The start date.</param>
        /// <param name="to">The end date.</param>
        private async Task ProcessLookupOperationAsync(OperationRequest operation, int vehicleId, long companyId, Dictionary<string, object> vehicleResults, DateTime? from, DateTime? to)
        {
            foreach (var lookupOp in operation.LookupOperations)
            {
                var lookup = new LookupOperation
                {
                    SourceColumn = lookupOp.SourceColumn,
                    OperationType = lookupOp.OperationType,
                    TargetColumn = lookupOp.TargetColumn,
                    Filters = lookupOp.Filters,
                    TargetDataType = lookupOp.TargetDataType,
                    Joins = lookupOp.Joins,
                    KeyColumn = lookupOp.KeyColumn,
                    KeyDataType = lookupOp.KeyDataType,
                    ReturnKeyColumn = lookupOp.ReturnKeyColumn
                };

                if (vehicleId > 0)
                {
                    lookup.Filters.Add(new FilterCondition
                    {
                        Column = "VehicleId",
                        Operator = "=",
                        Value = vehicleId.ToString()
                    });
                }

                var lookups = new List<LookupOperation> { lookup };
                var lookupResult = await _vzoneGenieService.GetLookupDataAsync(lookups, from, to, null, companyId);

                var targetKey = string.Format("{0}-{1}", lookup.TargetColumn, lookup.OperationType);
                var targetColumnName = lookup.TargetColumn.Contains(".") ? lookup.TargetColumn.Split('.').Last() : lookup.TargetColumn;
                vehicleResults[targetColumnName] = lookupResult.ContainsKey(targetKey) ? lookupResult[targetKey] : null;

                if (lookup.ReturnKeyColumn && !string.IsNullOrEmpty(lookup.KeyColumn))
                {
                    var keyColumnName = lookup.KeyColumn.Contains(".") ? lookup.KeyColumn.Split('.').Last() : lookup.KeyColumn;
                    var keyColumnKey = string.Format("{0}-{1}", keyColumnName, lookup.OperationType);
                    vehicleResults[keyColumnName] = lookupResult.ContainsKey(keyColumnKey) ? lookupResult[keyColumnKey] : null;
                }

                //var resultKey = string.Format("{0}-{1}", lookup.TargetColumn, lookup.OperationType);
                //var result = lookupResult.ContainsKey(resultKey) ? lookupResult[resultKey] : null;
                //vehicleResults[lookupOp.TargetColumn] = result;
            }
        }
    }
}