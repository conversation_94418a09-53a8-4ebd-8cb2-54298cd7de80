﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace VZoneTrack.Gps.V32.WebApi.Models
{  

    public class Allocations
    {
        public List<Operator> Operators { get; set; }
        public List<Helper> Helpers { get; set; }
    }

    public class Operator
    {
        public Personnel Personnel { get; set; }
        public List<Shift> Shifts { get; set; }
    }

    public class Helper
    {
        public Personnel Personnel { get; set; }
        public List<Shift> Shifts { get; set; }
    }

    public class VehicleAllocation
    {
        public List<Fleet> Vehicles { get; set; }      
        public string ResponseText { get; set; }
    }

    public class Shift
    {
        public string Name { get; set; }
        public string StartTime { get; set; }
        public int StartGracePeriod { get; set; }
        public string EndTime { get; set; }
        public int EndGracePeriod { get; set; }
        public int MaxAllocations { get; set; }
    }

    public class Fleet
    {
        public string AssetCode { get; set; }
        public string DisplayText { get; set; }
        public string RegistrationNo { get; set; }     
        public Allocations Allocations { get; set; }
        public DateTime ResponseTime { get; set; }
    }
}