﻿using log4net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Web;
using VZoneTrack.Gps.V32.Business;
using VZoneTrack.Gps.V32.WebApi.Models;


namespace VZoneTrack.Gps.V32.WebApi.Code
{
    public class AuthenticationMiddleware
    {
        private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        public VehicleAllocation GetVehicleAllocationInfo(int companyId, DateTime requestDate, List<long> vehicleList)
        {
            VehicleAllocation vehicleAllocation = new VehicleAllocation();
            List<Fleet> lstFleet = new List<Fleet>();

            try
            {
                foreach (long vehicleId in vehicleList)
                {
                    CaseAuthenticationBusiness caseAuthenticationBusiness = new CaseAuthenticationBusiness();
                    DataSet dsResult = caseAuthenticationBusiness.GetVehicleAllocationInfo(companyId, requestDate, Convert.ToInt32(vehicleId));
                    DataTable dtDriver = dsResult.Tables[0];
                    DataTable dtShift = dsResult.Tables[1];
                    DataTable dtRequestTime = dsResult.Tables[2];

                    Vehicle _tmpVehicle = EntityProvider.GetVehicleInfoFromVehicleId(vehicleId);
                    Fleet _fleet = new Fleet
                    {
                        AssetCode = _tmpVehicle.AssetCode,
                        RegistrationNo = _tmpVehicle.RegistrationNo,
                        DisplayText = _tmpVehicle.DisplayText,
                        ResponseTime = Convert.ToDateTime(dtRequestTime.Rows[0]["RequestTime"])
                    };

                    DataTable _dtDrivers = null;
                    var _driverValues = dtDriver.AsEnumerable().Where(x => x.Field<bool>("IsDriver") == true);
                    if (_driverValues.Any())
                    {
                        _dtDrivers = _driverValues.CopyToDataTable();
                    }
                    DataTable _dtHelpers = null;
                    var _helperValues = dtDriver.AsEnumerable().Where(x => x.Field<bool>("IsDriver") == false);
                    if (_helperValues.Any())
                    {
                        _dtHelpers = _helperValues.CopyToDataTable();
                    }
                    List<Operator> drivers = new List<Operator>();
                    if (_dtDrivers != null)
                    {
                        foreach (DataRow _drwDriver in _dtDrivers.Rows)
                        {
                            Operator _objOperator = new Operator();

                            int _driverId = Convert.ToInt32(_drwDriver["PersonnelId"]);
                            int _driverAllocationId = Convert.ToInt32(_drwDriver["VehiclePersonnelAllocationId"]);

                            Personnel _driver = new Personnel
                            {
                                Name = _drwDriver["Name"].ToString(),
                                Code = string.IsNullOrEmpty(_drwDriver["PersonnelCode"].ToString()) ? string.Empty : _drwDriver["PersonnelCode"].ToString(),
                                CardNumber = string.IsNullOrEmpty(_drwDriver["CardNumber"].ToString()) ? string.Empty : _drwDriver["CardNumber"].ToString()
                            };

                            _objOperator.Personnel = _driver;

                            DataTable _dtDriverShift = null;
                            var _driverShiftValues = dtShift.AsEnumerable().Where(x => x.Field<int>("VehiclePersonnelAllocationId") == _driverAllocationId);

                            if (_driverShiftValues.Any())
                                _dtDriverShift = _driverShiftValues.CopyToDataTable();

                            List<Shift> driverShifts = new List<Shift>();
                            if (_dtDriverShift != null)
                            {
                                foreach (DataRow _drwDriverShift in _dtDriverShift.Rows)
                                {
                                    Shift _driverShift = new Shift
                                    {
                                        Name = _drwDriverShift["ShiftName"].ToString(),
                                        StartTime = _drwDriverShift["StartTime"].ToString(),
                                        StartGracePeriod = Convert.ToInt32(_drwDriverShift["StartGracePeriodInMin"]),
                                        EndTime = _drwDriverShift["EndTime"].ToString(),
                                        EndGracePeriod = Convert.ToInt32(_drwDriverShift["EndGracePeriodInMin"]),
                                        MaxAllocations = Convert.ToInt32(_drwDriverShift["MaxAllocations"])
                                    };

                                    driverShifts.Add(_driverShift);
                                }
                            }

                            _objOperator.Shifts = driverShifts;

                            drivers.Add(_objOperator);
                        }
                    }

                    List<Helper> helpers = new List<Helper>();
                    if (_dtHelpers != null)
                    {
                        foreach (DataRow _drwHelper in _dtHelpers.Rows)
                        {
                            Helper _objHelper = new Helper();

                            int _helperId = Convert.ToInt32(_drwHelper["PersonnelId"]);
                            int _helperAllocationId = Convert.ToInt32(_drwHelper["VehiclePersonnelAllocationId"]);

                            Personnel _helper = new Personnel
                            {
                                Name = _drwHelper["Name"].ToString(),
                                Code = string.IsNullOrEmpty(_drwHelper["PersonnelCode"].ToString()) ? string.Empty : _drwHelper["PersonnelCode"].ToString(),
                                CardNumber = string.IsNullOrEmpty(_drwHelper["CardNumber"].ToString()) ? string.Empty : _drwHelper["CardNumber"].ToString()
                            };
                            _objHelper.Personnel = _helper;

                            DataTable _dtHelperShift = null;
                            var _helperShiftValues = dtShift.AsEnumerable().Where(x => x.Field<int>("VehiclePersonnelAllocationId") == _helperAllocationId);

                            if (_helperShiftValues.Any())
                                _dtHelperShift = _helperShiftValues.CopyToDataTable();

                            List<Shift> helperShifts = new List<Shift>();

                            if (_dtHelperShift != null)
                            {
                                foreach (DataRow _drwHelperShift in _dtHelperShift.Rows)
                                {
                                    Shift _helperShift = new Shift
                                    {
                                        Name = _drwHelperShift["ShiftName"].ToString(),
                                        StartTime = _drwHelperShift["StartTime"].ToString(),
                                        StartGracePeriod = Convert.ToInt32(_drwHelperShift["StartGracePeriodInMin"]),
                                        EndTime = _drwHelperShift["EndTime"].ToString(),
                                        EndGracePeriod = Convert.ToInt32(_drwHelperShift["EndGracePeriodInMin"]),
                                        MaxAllocations = Convert.ToInt32(_drwHelperShift["MaxAllocations"])
                                    };

                                    helperShifts.Add(_helperShift);
                                }
                            }

                            _objHelper.Shifts = helperShifts;

                            helpers.Add(_objHelper);
                        }
                    }

                    Allocations allocations = new Allocations
                    {
                        Operators = drivers,
                        Helpers = helpers
                    };

                    _fleet.Allocations = allocations;
                    lstFleet.Add(_fleet);
                }

                vehicleAllocation.Vehicles = lstFleet;
                vehicleAllocation.ResponseText = lstFleet.Count + " Vehicles processed";
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
            }

            return vehicleAllocation;
        }
    }
}