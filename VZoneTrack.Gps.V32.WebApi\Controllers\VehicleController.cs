﻿using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Web.Http;
using VZoneTrack.Gps.V32.WebApi.Code;
using VZoneTrack.Gps.V32.WebApi.Interfaces;
using VZoneTrack.Gps.V32.WebApi.Models;
using VZoneTrack.Gps.V32.WebApi.Services;

namespace VZoneTrack.Gps.V32.WebApi.Controllers
{
    [Authorize]
    public class VehicleController : ControllerBase
    {   
        private static readonly ILog _logger = LogManager.GetLogger(typeof(VZoneGenieController));

        [AuthorizeAndRateLimit]
        [HttpGet]
        [Route("Vehicle/UserAuthorizedVehicles")]
        public IHttpActionResult GetUserAuthorizedVehicles()
        {
            try
            {
                long companyId = GetCompanyId();
                int userId = GetUserId();

                if (companyId <= 0 || userId <= 0)
                {
                    return BadRequest("Invalid Company or User information.");
                }

                List<VehicleInfo> vehicleInfoCollection = EntityProvider.GetUserAuthorizedVehicles(companyId, userId);

                return Ok(new
                {
                    message = "SUCCESS",
                    data = vehicleInfoCollection
                });
            }
            catch (Exception ex)
            {
                _logger.Error(string.Format("Error in ProcessAsync - Request: {0}", ex));
                return InternalServerError(new Exception("An unexpected error occurred while processing your request. Please contact support."));
            }
        }

        [AuthorizeAndRateLimit]
        [HttpGet]
        [Route("Vehicle/GroupInfo/{VehicleId}")]
        public IHttpActionResult GetVehicleGroups([FromUri] long vehicleId)
        {
            try
            {
                long companyId = GetCompanyId();
                int userId = GetUserId();

                if (companyId <= 0 || userId <= 0)
                {
                    return BadRequest("Invalid Company or User information.");
                }

                if (vehicleId <= 0)
                {
                    return BadRequest("No such vehicle found");
                }

                List<VehicleBasicInfo> vehicleInfoCollection = EntityProvider.GetVehicleGroups(vehicleId);

                return Ok(new
                {
                    message = "SUCCESS",
                    data = vehicleInfoCollection
                });
            }
            catch (Exception ex)
            {
                _logger.Error(string.Format("Error in ProcessAsync - Request: {0}", ex));
                return InternalServerError(new Exception("An unexpected error occurred while processing your request. Please contact support."));
            }
        }

        /// <summary>
        /// Retrieves the company ID from the request properties.
        /// </summary>
        /// <returns>The company ID, or 0 if not found.</returns>
        private long GetCompanyId()
        {
            return Request.Properties.ContainsKey("CompanyId") ? (long)Request.Properties["CompanyId"] : 0;
        }

        /// <summary>
        /// Retrieves the company ID from the request properties.
        /// </summary>
        /// <returns>The company ID, or 0 if not found.</returns>
        private int GetUserId()
        {
            return Request.Properties.ContainsKey("UserId") ? (int)Request.Properties["UserId"] : 0;
        }
    }
}